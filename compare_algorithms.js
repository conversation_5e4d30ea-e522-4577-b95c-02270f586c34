#!/usr/bin/env node

/**
 * 对比原始算法和严格算法的评分差异
 */

const fs = require('fs').promises;
const DanceSimilarityCalculator = require('./danceSimilarity.js');
const StrictDanceSimilarityCalculator = require('./strictDanceSimilarity.js');

async function compareAlgorithms() {
    console.log('🔍 算法对比测试');
    console.log('='.repeat(60));
    
    try {
        // 加载数据
        const videoData = JSON.parse(await fs.readFile('videoPoint.json', 'utf8'));
        const userData = JSON.parse(await fs.readFile('userPoint.json', 'utf8'));
        
        console.log(`📊 数据: 视频${videoData.length}帧, 用户${userData.length}帧`);
        
        const originalCalculator = new DanceSimilarityCalculator();
        const strictCalculator = new StrictDanceSimilarityCalculator();
        
        // 测试单帧对比
        console.log('\n🧪 单帧相似度对比 (前10帧):');
        console.log('帧号\t原始算法\t严格算法\t差异');
        console.log('-'.repeat(50));
        
        const testFrames = Math.min(10, videoData.length, userData.length);
        let originalSum = 0, strictSum = 0;
        
        for (let i = 0; i < testFrames; i++) {
            const originalScore = originalCalculator.calculateFrameSimilarity(videoData[i], userData[i]);
            const strictScore = strictCalculator.calculateStrictFrameSimilarity(videoData[i], userData[i]);
            const diff = originalScore - strictScore;
            
            console.log(`${i + 1}\t${originalScore.toFixed(1)}\t\t${strictScore.toFixed(1)}\t\t${diff.toFixed(1)}`);
            
            originalSum += originalScore;
            strictSum += strictScore;
        }
        
        console.log('-'.repeat(50));
        console.log(`平均\t${(originalSum/testFrames).toFixed(1)}\t\t${(strictSum/testFrames).toFixed(1)}\t\t${((originalSum-strictSum)/testFrames).toFixed(1)}`);
        
        // 测试小数据集完整分析
        console.log('\n🎯 小数据集完整分析对比:');
        const smallVideoData = videoData.slice(0, 100);
        const smallUserData = userData.slice(0, 100);
        
        console.log('⏳ 运行原始算法...');
        const originalResult = await originalCalculator.quickScore(smallVideoData, smallUserData);
        
        console.log('⏳ 运行严格算法...');
        const strictResult = await strictCalculator.strictQuickScore(smallVideoData, smallUserData);
        
        console.log('\n📊 结果对比:');
        console.log('='.repeat(60));
        console.log(`原始算法: ${originalResult.score}/100 (${originalResult.grade}) - ${originalResult.level}`);
        console.log(`严格算法: ${strictResult.score}/100 (${strictResult.grade}) - ${strictResult.level}`);
        console.log(`分数差异: ${(originalResult.score - strictResult.score).toFixed(2)}分`);
        
        console.log('\n📈 分段分析对比:');
        console.log('段落长度\t原始算法\t严格算法\t差异');
        console.log('-'.repeat(50));
        
        for (let i = 0; i < Math.min(originalResult.segmentResults.length, strictResult.segmentResults.length); i++) {
            const orig = originalResult.segmentResults[i];
            const strict = strictResult.segmentResults[i];
            const diff = orig.score - strict.score;
            
            console.log(`${orig.segmentType}\t\t${orig.score}\t\t${strict.score}\t\t${diff.toFixed(1)}`);
        }
        
        // 测试极端情况
        console.log('\n🧪 极端情况测试:');
        await testExtremeCases(originalCalculator, strictCalculator);
        
    } catch (error) {
        console.error(`❌ 测试失败: ${error.message}`);
    }
}

async function testExtremeCases(originalCalc, strictCalc) {
    // 创建标准姿态
    const standardPose = [
        {x: 0.5, y: 0.3},   // 鼻子
        {x: 0.48, y: 0.28}, {x: 0.52, y: 0.28}, // 眼睛
        {x: 0.46, y: 0.26}, {x: 0.54, y: 0.26}, // 耳朵
        {x: 0.4, y: 0.4}, {x: 0.6, y: 0.4},     // 肩膀
        {x: 0.35, y: 0.5}, {x: 0.65, y: 0.5},   // 肘部
        {x: 0.3, y: 0.6}, {x: 0.7, y: 0.6},     // 手腕
        {x: 0.45, y: 0.7}, {x: 0.55, y: 0.7},   // 髋部
        {x: 0.43, y: 0.85}, {x: 0.57, y: 0.85}, // 膝盖
        {x: 0.41, y: 0.95}, {x: 0.59, y: 0.95}  // 脚踝
    ];
    
    // 测试1: 完全相同
    console.log('  1. 完全相同姿态:');
    const origSame = originalCalc.calculateFrameSimilarity(standardPose, standardPose);
    const strictSame = strictCalc.calculateStrictFrameSimilarity(standardPose, standardPose);
    console.log(`     原始: ${origSame.toFixed(1)}, 严格: ${strictSame.toFixed(1)}`);
    
    // 测试2: 轻微差异 (1%偏移)
    const slight = standardPose.map(p => ({
        x: p.x + (Math.random() - 0.5) * 0.02,
        y: p.y + (Math.random() - 0.5) * 0.02
    }));
    console.log('  2. 轻微差异 (1%偏移):');
    const origSlight = originalCalc.calculateFrameSimilarity(standardPose, slight);
    const strictSlight = strictCalc.calculateStrictFrameSimilarity(standardPose, slight);
    console.log(`     原始: ${origSlight.toFixed(1)}, 严格: ${strictSlight.toFixed(1)}`);
    
    // 测试3: 中等差异 (5%偏移)
    const medium = standardPose.map(p => ({
        x: p.x + (Math.random() - 0.5) * 0.1,
        y: p.y + (Math.random() - 0.5) * 0.1
    }));
    console.log('  3. 中等差异 (5%偏移):');
    const origMedium = originalCalc.calculateFrameSimilarity(standardPose, medium);
    const strictMedium = strictCalc.calculateStrictFrameSimilarity(standardPose, medium);
    console.log(`     原始: ${origMedium.toFixed(1)}, 严格: ${strictMedium.toFixed(1)}`);
    
    // 测试4: 大差异 (20%偏移)
    const large = standardPose.map(p => ({
        x: p.x + (Math.random() - 0.5) * 0.4,
        y: p.y + (Math.random() - 0.5) * 0.4
    }));
    console.log('  4. 大差异 (20%偏移):');
    const origLarge = originalCalc.calculateFrameSimilarity(standardPose, large);
    const strictLarge = strictCalc.calculateStrictFrameSimilarity(standardPose, large);
    console.log(`     原始: ${origLarge.toFixed(1)}, 严格: ${strictLarge.toFixed(1)}`);
    
    // 测试5: 完全随机
    const random = standardPose.map(() => ({
        x: Math.random(),
        y: Math.random()
    }));
    console.log('  5. 完全随机:');
    const origRandom = originalCalc.calculateFrameSimilarity(standardPose, random);
    const strictRandom = strictCalc.calculateStrictFrameSimilarity(standardPose, random);
    console.log(`     原始: ${origRandom.toFixed(1)}, 严格: ${strictRandom.toFixed(1)}`);
}

async function main() {
    await compareAlgorithms();
    
    console.log('\n✅ 对比测试完成!');
    console.log('\n💡 建议:');
    console.log('   - 如果原始算法分数过高，使用严格算法');
    console.log('   - 严格算法更能反映真实的动作差异');
    console.log('   - 可以根据需要调整评分标准');
}

if (require.main === module) {
    main().catch(error => {
        console.error(`❌ 程序执行失败: ${error.message}`);
        process.exit(1);
    });
}
