#!/usr/bin/env node

/**
 * JavaScript版本舞蹈相似度计算器测试脚本
 */

const fs = require('fs').promises;
const path = require('path');
const DanceSimilarityCalculator = require('./danceScore.js');

class TestRunner {
    constructor() {
        this.calculator = new DanceSimilarityCalculator();
        this.testResults = [];
    }

    async runTest(testName, testFunction) {
        console.log(`\n🧪 测试: ${testName}`);
        try {
            const startTime = Date.now();
            await testFunction();
            const duration = Date.now() - startTime;
            console.log(`   ✅ 通过 (${duration}ms)`);
            this.testResults.push({ name: testName, status: 'PASS', duration });
        } catch (error) {
            console.log(`   ❌ 失败: ${error.message}`);
            this.testResults.push({ name: testName, status: 'FAIL', error: error.message });
        }
    }

    async testDataLoading() {
        const files = ['videoPoint.json', 'userPoint.json'];
        
        for (const file of files) {
            if (await this.fileExists(file)) {
                const data = JSON.parse(await fs.readFile(file, 'utf8'));
                if (Array.isArray(data) && data.length > 0) {
                    console.log(`     ✓ ${file}: ${data.length}帧数据`);
                } else {
                    throw new Error(`${file} 数据格式错误`);
                }
            } else {
                throw new Error(`${file} 文件不存在`);
            }
        }
    }

    async testSimilarityCalculation() {
        // 创建测试数据
        const testVideoFrame = [
            {x: 0.5, y: 0.3}, {x: 0.48, y: 0.28}, {x: 0.52, y: 0.28},
            {x: 0.46, y: 0.26}, {x: 0.54, y: 0.26}, {x: 0.4, y: 0.4},
            {x: 0.6, y: 0.4}, {x: 0.35, y: 0.5}, {x: 0.65, y: 0.5},
            {x: 0.3, y: 0.6}, {x: 0.7, y: 0.6}, {x: 0.45, y: 0.7},
            {x: 0.55, y: 0.7}, {x: 0.43, y: 0.85}, {x: 0.57, y: 0.85},
            {x: 0.41, y: 0.95}, {x: 0.59, y: 0.95}
        ];

        const testUserFrame = [
            {x: 0.51, y: 0.31}, {x: 0.49, y: 0.29}, {x: 0.53, y: 0.29},
            {x: 0.47, y: 0.27}, {x: 0.55, y: 0.27}, {x: 0.41, y: 0.41},
            {x: 0.61, y: 0.41}, {x: 0.36, y: 0.51}, {x: 0.66, y: 0.51},
            {x: 0.31, y: 0.61}, {x: 0.71, y: 0.61}, {x: 0.46, y: 0.71},
            {x: 0.56, y: 0.71}, {x: 0.44, y: 0.86}, {x: 0.58, y: 0.86},
            {x: 0.42, y: 0.96}, {x: 0.60, y: 0.96}
        ];

        const similarity = this.calculator.calculateFrameSimilarity(testVideoFrame, testUserFrame);
        
        if (similarity >= 0 && similarity <= 100) {
            console.log(`     ✓ 相似度计算: ${similarity.toFixed(2)}/100`);
        } else {
            throw new Error(`相似度计算结果异常: ${similarity}`);
        }
    }

    async testBestMatchFinding() {
        // 创建简单的测试数据
        const videoData = [];
        const userData = [];
        
        for (let i = 0; i < 20; i++) {
            const frame = [];
            for (let j = 0; j < 17; j++) {
                frame.push({
                    x: 0.5 + Math.sin(i * 0.1) * 0.1,
                    y: 0.5 + Math.cos(i * 0.1) * 0.1
                });
            }
            videoData.push(frame);
            
            // 用户数据稍有偏移
            const userFrame = frame.map(point => ({
                x: point.x + (Math.random() - 0.5) * 0.02,
                y: point.y + (Math.random() - 0.5) * 0.02
            }));
            userData.push(userFrame);
        }

        const result = this.calculator.findBestMatchingSegment(videoData, userData, 5);
        
        if (result.score >= 0 && result.score <= 100) {
            console.log(`     ✓ 最佳匹配: ${result.score}/100 (视频${result.videoStart}, 用户${result.userStart})`);
        } else {
            throw new Error(`最佳匹配结果异常: ${result.score}`);
        }
    }

    async testQuickScore() {
        const videoData = JSON.parse(await fs.readFile('videoPoint.json', 'utf8'));
        const userData = JSON.parse(await fs.readFile('userPoint.json', 'utf8'));

        const result = await this.calculator.quickScore(videoData, userData);
        
        if (result.error) {
            throw new Error(result.error);
        }

        if (result.score >= 0 && result.score <= 100) {
            console.log(`     ✓ 快速评分: ${result.score}/100 (${result.grade})`);
            console.log(`     ✓ 数据量: 视频${result.videoFrames}帧, 用户${result.userFrames}帧`);
        } else {
            throw new Error(`快速评分结果异常: ${result.score}`);
        }
    }

    async testPerformance() {
        const videoData = JSON.parse(await fs.readFile('videoPoint.json', 'utf8'));
        const userData = JSON.parse(await fs.readFile('userPoint.json', 'utf8'));

        const startTime = Date.now();
        await this.calculator.quickScore(videoData, userData);
        const duration = Date.now() - startTime;

        console.log(`     ✓ 性能测试: ${duration}ms (${videoData.length + userData.length}帧数据)`);
        
        if (duration > 30000) { // 30秒
            throw new Error(`性能测试超时: ${duration}ms`);
        }
    }

    async testHTMLFileExists() {
        const htmlFile = 'dance_comparison.html';
        if (await this.fileExists(htmlFile)) {
            const content = await fs.readFile(htmlFile, 'utf8');
            if (content.includes('DanceSimilarityCalculator') && content.includes('canvas')) {
                console.log(`     ✓ HTML文件存在且包含必要组件`);
            } else {
                throw new Error('HTML文件缺少必要组件');
            }
        } else {
            throw new Error('HTML文件不存在');
        }
    }

    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    printSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 测试结果汇总');
        console.log('='.repeat(60));

        const passed = this.testResults.filter(r => r.status === 'PASS').length;
        const failed = this.testResults.filter(r => r.status === 'FAIL').length;
        const total = this.testResults.length;

        console.log(`总测试数: ${total}`);
        console.log(`通过: ${passed} ✅`);
        console.log(`失败: ${failed} ❌`);
        console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults
                .filter(r => r.status === 'FAIL')
                .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
        }

        const totalDuration = this.testResults
            .filter(r => r.duration)
            .reduce((sum, r) => sum + r.duration, 0);
        
        console.log(`\n⏱️  总耗时: ${totalDuration}ms`);
        
        if (passed === total) {
            console.log('\n🎉 所有测试通过！JavaScript版本工作正常。');
        } else {
            console.log('\n⚠️  部分测试失败，请检查相关功能。');
        }
    }

    async runAllTests() {
        console.log('🚀 开始JavaScript版本功能测试');
        console.log('='.repeat(60));

        await this.runTest('数据文件加载', () => this.testDataLoading());
        await this.runTest('相似度计算', () => this.testSimilarityCalculation());
        await this.runTest('最佳匹配查找', () => this.testBestMatchFinding());
        await this.runTest('快速评分', () => this.testQuickScore());
        await this.runTest('性能测试', () => this.testPerformance());
        await this.runTest('HTML文件检查', () => this.testHTMLFileExists());

        this.printSummary();
    }
}

// 运行测试
if (require.main === module) {
    const testRunner = new TestRunner();
    testRunner.runAllTests().catch(error => {
        console.error(`❌ 测试运行失败: ${error.message}`);
        process.exit(1);
    });
}

module.exports = TestRunner;
