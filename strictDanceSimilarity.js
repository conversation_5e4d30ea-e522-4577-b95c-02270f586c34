/**
 * 严格版本的舞蹈动作相似度计算器
 * 更加严格的评分标准，避免虚高的分数
 */

class StrictDanceSimilarityCalculator {
    constructor() {
        // 更严格的权重设置
        this.pointWeights = [
            0.1,  // 0: 鼻子 - 不重要
            0.1,  // 1: 左眼 - 不重要
            0.1,  // 2: 右眼 - 不重要
            0.1,  // 3: 左耳 - 不重要
            0.1,  // 4: 右耳 - 不重要
            5.0,  // 5: 左肩 - 非常重要
            5.0,  // 6: 右肩 - 非常重要
            4.0,  // 7: 左肘 - 重要
            4.0,  // 8: 右肘 - 重要
            3.0,  // 9: 左腕 - 中等重要
            3.0,  // 10: 右腕 - 中等重要
            6.0,  // 11: 左髋 - 最重要
            6.0,  // 12: 右髋 - 最重要
            5.0,  // 13: 左膝 - 非常重要
            5.0,  // 14: 右膝 - 非常重要
            4.0,  // 15: 左踝 - 重要
            4.0   // 16: 右踝 - 重要
        ];
        
        // 关键连接线用于角度计算
        this.keyConnections = [
            [5, 6],   // 肩膀线
            [11, 12], // 髋部线
            [5, 11],  // 左侧躯干
            [6, 12],  // 右侧躯干
            [5, 7],   // 左上臂
            [6, 8],   // 右上臂
            [7, 9],   // 左前臂
            [8, 10],  // 右前臂
            [11, 13], // 左大腿
            [12, 14], // 右大腿
            [13, 15], // 左小腿
            [14, 16]  // 右小腿
        ];
    }

    /**
     * 计算人体中心点和缩放因子
     */
    calculateBodyMetrics(points) {
        if (!points || points.length < 17) return null;
        
        // 计算肩膀中心
        const shoulderCenter = {
            x: (points[5].x + points[6].x) / 2,
            y: (points[5].y + points[6].y) / 2
        };
        
        // 计算髋部中心
        const hipCenter = {
            x: (points[11].x + points[12].x) / 2,
            y: (points[11].y + points[12].y) / 2
        };
        
        // 计算躯干长度
        const torsoLength = Math.sqrt(
            Math.pow(shoulderCenter.x - hipCenter.x, 2) + 
            Math.pow(shoulderCenter.y - hipCenter.y, 2)
        );
        
        // 计算肩宽
        const shoulderWidth = Math.sqrt(
            Math.pow(points[5].x - points[6].x, 2) + 
            Math.pow(points[5].y - points[6].y, 2)
        );
        
        return {
            shoulderCenter,
            hipCenter,
            torsoLength: torsoLength || 0.1, // 避免除零
            shoulderWidth: shoulderWidth || 0.1,
            bodyCenter: {
                x: (shoulderCenter.x + hipCenter.x) / 2,
                y: (shoulderCenter.y + hipCenter.y) / 2
            }
        };
    }

    /**
     * 严格的姿态归一化
     */
    strictNormalizePose(points) {
        const metrics = this.calculateBodyMetrics(points);
        if (!metrics) return points;
        
        // 使用躯干长度作为标准化单位
        const scale = metrics.torsoLength;
        const center = metrics.bodyCenter;
        
        return points.map(point => {
            if (!point || point.x === undefined || point.y === undefined) {
                return point;
            }
            return {
                x: (point.x - center.x) / scale,
                y: (point.y - center.y) / scale
            };
        });
    }

    /**
     * 计算关键角度
     */
    calculateKeyAngles(points) {
        const angles = [];
        
        for (const [p1, p2] of this.keyConnections) {
            if (points[p1] && points[p2]) {
                const angle = Math.atan2(
                    points[p2].y - points[p1].y,
                    points[p2].x - points[p1].x
                );
                angles.push(angle);
            } else {
                angles.push(null);
            }
        }
        
        return angles;
    }

    /**
     * 计算角度相似度 - 更严格的版本
     */
    calculateStrictAngleSimilarity(videoAngles, userAngles) {
        let totalSimilarity = 0;
        let validAngles = 0;
        
        for (let i = 0; i < Math.min(videoAngles.length, userAngles.length); i++) {
            if (videoAngles[i] !== null && userAngles[i] !== null) {
                let angleDiff = Math.abs(videoAngles[i] - userAngles[i]);
                if (angleDiff > Math.PI) angleDiff = 2 * Math.PI - angleDiff;
                
                // 更严格的角度评分：30度以内才有高分
                const maxAllowedDiff = Math.PI / 6; // 30度
                let similarity;
                
                if (angleDiff <= maxAllowedDiff) {
                    similarity = 100 * (1 - angleDiff / maxAllowedDiff);
                } else {
                    // 超过30度的差异给予很低的分数
                    similarity = Math.max(0, 20 * (1 - (angleDiff - maxAllowedDiff) / (Math.PI - maxAllowedDiff)));
                }
                
                totalSimilarity += similarity;
                validAngles++;
            }
        }
        
        return validAngles > 0 ? totalSimilarity / validAngles : 0;
    }

    /**
     * 计算位置相似度 - 更严格的版本
     */
    calculateStrictPositionSimilarity(videoPoints, userPoints) {
        let totalWeightedScore = 0;
        let totalWeight = 0;
        
        for (let i = 0; i < Math.min(videoPoints.length, userPoints.length, this.pointWeights.length); i++) {
            if (videoPoints[i] && userPoints[i]) {
                const distance = Math.sqrt(
                    Math.pow(videoPoints[i].x - userPoints[i].x, 2) + 
                    Math.pow(videoPoints[i].y - userPoints[i].y, 2)
                );
                
                // 更严格的距离阈值：0.15个躯干长度以内才有高分
                const maxAllowedDistance = 0.15;
                let similarity;
                
                if (distance <= maxAllowedDistance) {
                    similarity = 100 * (1 - distance / maxAllowedDistance);
                } else {
                    // 超过阈值的距离给予很低的分数
                    similarity = Math.max(0, 10 * (1 - (distance - maxAllowedDistance) / (1 - maxAllowedDistance)));
                }
                
                const weight = this.pointWeights[i];
                totalWeightedScore += similarity * weight;
                totalWeight += weight;
            }
        }
        
        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    }

    /**
     * 严格的单帧相似度计算
     */
    calculateStrictFrameSimilarity(videoFrame, userFrame) {
        if (!videoFrame || !userFrame) return 0;
        if (videoFrame.length !== userFrame.length) return 0;

        // 归一化姿态
        const normalizedVideo = this.strictNormalizePose(videoFrame);
        const normalizedUser = this.strictNormalizePose(userFrame);
        
        // 计算关键角度
        const videoAngles = this.calculateKeyAngles(normalizedVideo);
        const userAngles = this.calculateKeyAngles(normalizedUser);
        
        // 计算角度相似度 (权重70%)
        const angleScore = this.calculateStrictAngleSimilarity(videoAngles, userAngles);
        
        // 计算位置相似度 (权重30%)
        const positionScore = this.calculateStrictPositionSimilarity(normalizedVideo, normalizedUser);
        
        // 综合得分
        const finalScore = angleScore * 0.7 + positionScore * 0.3;
        
        return Math.max(0, Math.min(100, finalScore));
    }

    /**
     * 严格的最佳匹配查找
     */
    findStrictBestMatch(videoData, userData, segmentLength = 10) {
        if (!videoData || !userData || videoData.length === 0 || userData.length === 0) {
            return { score: 0, videoStart: 0, userStart: 0 };
        }

        let bestScore = 0;
        let bestVideoStart = 0;
        let bestUserStart = 0;

        // 更密集的搜索，但限制范围以提高性能
        const maxSearchFrames = 100;
        const videoStep = Math.max(1, Math.floor(videoData.length / maxSearchFrames));
        const userStep = Math.max(1, Math.floor(userData.length / maxSearchFrames));

        for (let userStart = 0; userStart < userData.length - segmentLength; userStart += userStep) {
            for (let videoStart = 0; videoStart < videoData.length - segmentLength; videoStart += videoStep) {
                let segmentScore = 0;
                let validFrames = 0;

                for (let i = 0; i < segmentLength; i++) {
                    if (userStart + i < userData.length && videoStart + i < videoData.length) {
                        const frameScore = this.calculateStrictFrameSimilarity(
                            videoData[videoStart + i],
                            userData[userStart + i]
                        );
                        segmentScore += frameScore;
                        validFrames++;
                    }
                }

                if (validFrames > 0) {
                    const avgScore = segmentScore / validFrames;
                    if (avgScore > bestScore) {
                        bestScore = avgScore;
                        bestVideoStart = videoStart;
                        bestUserStart = userStart;
                    }
                }
            }
        }

        return {
            score: Math.round(bestScore * 100) / 100,
            videoStart: bestVideoStart,
            userStart: bestUserStart,
            segmentLength: segmentLength
        };
    }

    /**
     * 严格的快速评分
     */
    async strictQuickScore(videoData, userData) {
        if (!videoData || !userData) {
            return { error: "数据无效", score: 0 };
        }

        console.log(`📊 数据信息: 视频${videoData.length}帧, 用户${userData.length}帧`);

        const segmentLengths = [5, 10, 15];
        const results = [];

        for (const length of segmentLengths) {
            if (length <= Math.min(videoData.length, userData.length)) {
                console.log(`🔄 严格计算${length}帧段落相似度...`);
                const result = this.findStrictBestMatch(videoData, userData, length);
                result.segmentType = `${length}帧`;
                results.push(result);
            }
        }

        if (results.length === 0) {
            return { error: "无法计算相似度", score: 0 };
        }

        const mainResult = results.find(r => r.segmentType === "10帧") || results[results.length - 1];
        const overallScore = mainResult.score;

        // 更严格的评级标准
        let grade, level;
        if (overallScore >= 95) {
            grade = "A+"; level = "完美";
        } else if (overallScore >= 90) {
            grade = "A"; level = "优秀";
        } else if (overallScore >= 85) {
            grade = "B+"; level = "良好";
        } else if (overallScore >= 80) {
            grade = "B"; level = "中等偏上";
        } else if (overallScore >= 70) {
            grade = "C+"; level = "中等";
        } else if (overallScore >= 60) {
            grade = "C"; level = "及格";
        } else {
            grade = "D"; level = "需要努力";
        }

        return {
            score: overallScore,
            grade: grade,
            level: level,
            segmentResults: results,
            videoFrames: videoData.length,
            userFrames: userData.length,
            bestMatchInfo: {
                videoStart: mainResult.videoStart,
                userStart: mainResult.userStart,
                segmentLength: mainResult.segmentLength
            }
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StrictDanceSimilarityCalculator;
}

if (typeof window !== 'undefined') {
    window.StrictDanceSimilarityCalculator = StrictDanceSimilarityCalculator;
}
