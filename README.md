# 舞蹈动作相似度计算器

这是一个用于计算用户舞蹈动作与标准视频动作相似度的Python工具包，可以给出0-100分的评分。

## 📁 文件说明

### 核心文件
- `fast_dance_scorer.py` - **推荐使用** 快速高效的舞蹈评分器
- `simple_dance_scorer.py` - 简单易懂的评分器，适合学习和理解算法
- `dance_similarity.py` - 完整功能的高级计算器，提供详细分析

### 数据文件
- `video.json` - 标准舞蹈视频的动作数据 (868帧)
- `user.json` - 用户跳舞的实时动作数据 (781帧)

### 测试文件
- `test_dance_similarity.py` - 测试脚本
- `README.md` - 使用说明文档

## 🎯 数据格式

每个JSON文件包含一个二维数组，每一行代表一帧动作，包含8个角度值：

```json
[
    [103.58, 182.57, 267.12, 160.19, 93.13, 181.92, 276.25, 266.50],
    [101.63, 181.60, 268.63, 169.71, 90.56, 161.43, 271.99, 171.56],
    ...
]
```

8个角度分别对应：
1. `calculateAngle(RIGHT_SHOULDER, LEFT_SHOULDER, LEFT_ELBOW)` - 左臂角度
2. `calculateAngle(LEFT_SHOULDER, LEFT_ELBOW, LEFT_WRIST)` - 左前臂角度  
3. `calculateAngle(LEFT_SHOULDER, RIGHT_SHOULDER, RIGHT_ELBOW)` - 右臂角度
4. `calculateAngle(RIGHT_SHOULDER, RIGHT_ELBOW, RIGHT_WRIST)` - 右前臂角度
5. `calculateAngle(RIGHT_HIP, LEFT_HIP, LEFT_KNEE)` - 左腿角度
6. `calculateAngle(LEFT_HIP, LEFT_KNEE, LEFT_ANKLE)` - 左小腿角度
7. `calculateAngle(LEFT_HIP, RIGHT_HIP, RIGHT_KNEE)` - 右腿角度
8. `calculateAngle(RIGHT_HIP, RIGHT_KNEE, RIGHT_ANKLE)` - 右小腿角度

## 🚀 快速开始

### 方法1: 命令行工具 (最简单)

```bash
# 基本使用 - 显示完整报告
python dance_score.py

# 只显示得分
python dance_score.py --quick

# 详细分析特定段落 (视频第470帧, 用户第584帧, 分析5帧)
python dance_score.py --detail 470 584 5

# 使用自定义文件
python dance_score.py my_video.json my_user.json
```

### 方法2: Python代码调用

```python
from fast_dance_scorer import quick_dance_score, print_quick_report

# 快速计算得分
result = quick_dance_score("video.json", "user.json")
print_quick_report(result)
```

### 方法3: 获取简单得分

```python
from fast_dance_scorer import quick_dance_score

result = quick_dance_score()
print(f"舞蹈得分: {result['score']}/100")
print(f"评级: {result['grade']} ({result['level']})")
```

### 方法4: 详细段落分析

```python
from fast_dance_scorer import analyze_specific_segment

# 分析特定段落 (视频第100帧开始，用户第50帧开始，分析10帧)
detail = analyze_specific_segment("video.json", "user.json", 100, 50, 10)
print(f"段落得分: {detail['overall_score']}/100")
```

## 📊 评分系统

### 评级标准
- **A+ (90-100分)**: 完美 - 动作非常标准
- **A (85-89分)**: 优秀 - 动作很好
- **B+ (80-84分)**: 良好 - 动作基本正确
- **B (75-79分)**: 中等偏上 - 需要细节完善
- **C+ (70-74分)**: 中等 - 需要更多练习
- **C (60-69分)**: 及格 - 动作需要改进
- **D (0-59分)**: 需要努力 - 建议从基础开始

### 权重设置
不同身体部位有不同的重要性权重：
- 腿部动作权重更高 (1.5, 1.2)
- 主要关节权重较高 (1.0, 1.2)
- 次要关节权重适中 (0.8)

## 🔧 算法原理

### 1. 角度差值计算
```python
def angle_difference(angle1, angle2):
    """计算两个角度的最小差值 (0-180度)"""
    angle1 = angle1 % 360
    angle2 = angle2 % 360
    diff = abs(angle1 - angle2)
    return min(diff, 360 - diff)
```

### 2. 相似度转换
```python
similarity = max(0, 100 - (angle_diff / 180) * 100)
```
角度差值越小，相似度越高。

### 3. 加权平均
```python
weighted_score = sum(similarity[i] * weight[i] for i in range(8)) / sum(weights)
```

### 4. 最佳匹配
使用滑动窗口在视频数据中寻找与用户动作最匹配的段落。

## 📈 性能优化

`fast_dance_scorer.py` 使用了以下优化技术：

1. **NumPy向量化计算** - 大幅提升计算速度
2. **数据采样** - 处理大数据集时自动采样
3. **矩阵广播** - 高效计算相似度矩阵
4. **内存优化** - 减少不必要的数据复制

## 🧪 测试

运行测试脚本：
```bash
python test_dance_similarity.py
```

或直接运行快速评分器：
```bash
python fast_dance_scorer.py
```

## 📝 示例输出

### 基本分析报告
```
🎭 快速舞蹈分析报告
==================================================
📊 总体得分: 91.82/100
🏆 评级: A+ (完美)
📋 数据: 视频868帧, 用户781帧

📈 分段分析:
   5帧: 94.72/100
   10帧: 91.82/100
   15帧: 89.99/100

🎯 最佳匹配: 视频第470帧 vs 用户第584帧 (10帧段落)
```

### 详细段落分析
```
🔍 分析段落: 视频第470帧, 用户第584帧, 长度5帧

📊 段落总分: 89.77/100
📋 段落信息: 视频帧470-474, 用户帧584-588

📈 逐帧分析:
  第1帧: 88.0/100
    最佳: 左小腿 (98.5/100)
    最差: 左前臂 (54.1/100, 差值82.7°)
  第2帧: 90.2/100
    最佳: 左小腿 (96.9/100)
    最差: 左前臂 (55.1/100, 差值80.8°)
  第3帧: 90.3/100
    最佳: 左小腿 (98.8/100)
    最差: 左前臂 (55.5/100, 差值80.0°)
```

### 快速得分
```bash
$ python dance_score.py --quick
得分: 91.82/100 (A+)
```

## 🛠️ 自定义使用

### 修改权重
```python
# 在fast_dance_scorer.py中修改权重
weights = np.array([1.0, 0.8, 1.0, 0.8, 1.5, 1.2, 1.5, 1.2])
```

### 调整段落长度
```python
result = quick_dance_score()
# 或指定不同的段落长度
result = find_best_segment_fast(video_data, user_data, segment_length=20)
```

### 处理其他数据文件
```python
result = quick_dance_score("my_video.json", "my_user.json")
```

## ⚠️ 注意事项

1. **数据格式**: 确保JSON文件格式正确，每帧包含8个角度值
2. **文件路径**: 确保数据文件在正确的路径下
3. **数据量**: 大数据集会自动采样以提高性能
4. **角度单位**: 假设输入角度为度数 (0-360)

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

MIT License
