import json
import numpy as np
from typing import List, Dict, <PERSON><PERSON>

def load_dance_data(file_path: str) -> np.ndarray:
    """加载舞蹈数据并转换为numpy数组以提高计算效率"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return np.array(data, dtype=np.float32)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {e}")
        return np.array([])

def angle_difference_vectorized(angles1: np.ndarray, angles2: np.ndarray) -> np.ndarray:
    """向量化计算角度差值"""
    # 标准化角度到0-360
    angles1 = angles1 % 360
    angles2 = angles2 % 360
    
    # 计算差值
    diff = np.abs(angles1 - angles2)
    
    # 选择最小差值
    return np.minimum(diff, 360 - diff)

def calculate_similarity_matrix(video_data: np.ndarray, user_data: np.ndarray, 
                               weights: np.ndarray = None) -> np.ndarray:
    """
    计算相似度矩阵 - 高效版本
    
    Args:
        video_data: 标准视频数据 (n_frames, 8)
        user_data: 用户数据 (m_frames, 8)
        weights: 8个角度的权重
    
    Returns:
        相似度矩阵 (m_frames, n_frames)
    """
    if weights is None:
        weights = np.array([1.0, 0.8, 1.0, 0.8, 1.5, 1.2, 1.5, 1.2])
    
    # 扩展维度进行广播计算
    user_expanded = user_data[:, np.newaxis, :]  # (m, 1, 8)
    video_expanded = video_data[np.newaxis, :, :]  # (1, n, 8)
    
    # 计算所有帧对之间的角度差值
    angle_diffs = angle_difference_vectorized(user_expanded, video_expanded)  # (m, n, 8)
    
    # 转换为相似度分数
    similarities = np.maximum(0, 100 - (angle_diffs / 180) * 100)  # (m, n, 8)
    
    # 应用权重并计算加权平均
    weighted_similarities = similarities * weights[np.newaxis, np.newaxis, :]
    similarity_matrix = np.sum(weighted_similarities, axis=2) / np.sum(weights)
    
    return similarity_matrix

def find_best_segment_fast(video_data: np.ndarray, user_data: np.ndarray, 
                          segment_length: int = 10) -> Dict:
    """
    快速找到最佳匹配段落
    
    Args:
        video_data: 标准视频数据
        user_data: 用户数据
        segment_length: 段落长度
    
    Returns:
        匹配结果
    """
    if len(user_data) < segment_length or len(video_data) < segment_length:
        return {"score": 0.0, "video_start": 0, "user_start": 0}
    
    # 采样策略：如果数据太大，进行采样
    max_frames = 200  # 最大处理帧数
    
    if len(video_data) > max_frames:
        # 均匀采样视频数据
        video_indices = np.linspace(0, len(video_data) - 1, max_frames, dtype=int)
        video_sample = video_data[video_indices]
    else:
        video_sample = video_data
        video_indices = np.arange(len(video_data))
    
    if len(user_data) > max_frames:
        # 均匀采样用户数据
        user_indices = np.linspace(0, len(user_data) - 1, max_frames, dtype=int)
        user_sample = user_data[user_indices]
    else:
        user_sample = user_data
        user_indices = np.arange(len(user_data))
    
    # 计算相似度矩阵
    similarity_matrix = calculate_similarity_matrix(video_sample, user_sample)
    
    best_score = 0.0
    best_video_start = 0
    best_user_start = 0
    
    # 寻找最佳段落
    for i in range(len(user_sample) - segment_length + 1):
        for j in range(len(video_sample) - segment_length + 1):
            # 计算段落平均相似度
            segment_scores = similarity_matrix[i:i+segment_length, j:j+segment_length]
            avg_score = np.mean(np.diag(segment_scores))
            
            if avg_score > best_score:
                best_score = avg_score
                best_video_start = video_indices[j] if len(video_data) > max_frames else j
                best_user_start = user_indices[i] if len(user_data) > max_frames else i
    
    return {
        "score": round(float(best_score), 2),
        "video_start": int(best_video_start),
        "user_start": int(best_user_start),
        "segment_length": segment_length
    }

def quick_dance_score(video_file: str = "video.json", user_file: str = "user.json") -> Dict:
    """
    快速计算舞蹈得分
    
    Returns:
        包含得分和基本信息的字典
    """
    # 加载数据
    video_data = load_dance_data(video_file)
    user_data = load_dance_data(user_file)
    
    if video_data.size == 0 or user_data.size == 0:
        return {"error": "数据加载失败", "score": 0.0}
    
    print(f"📊 数据信息: 视频{len(video_data)}帧, 用户{len(user_data)}帧")
    
    # 快速计算多个段落的得分
    segment_lengths = [5, 10, 15]
    results = []
    
    for length in segment_lengths:
        if length <= min(len(video_data), len(user_data)):
            print(f"🔄 计算{length}帧段落相似度...")
            result = find_best_segment_fast(video_data, user_data, length)
            result["segment_type"] = f"{length}帧"
            results.append(result)
    
    if not results:
        return {"error": "无法计算相似度", "score": 0.0}
    
    # 使用10帧段落作为主要得分，如果没有则使用最长的
    main_result = next((r for r in results if r["segment_type"] == "10帧"), results[-1])
    overall_score = main_result["score"]
    
    # 生成评级
    if overall_score >= 90:
        grade, level = "A+", "完美"
    elif overall_score >= 85:
        grade, level = "A", "优秀"
    elif overall_score >= 80:
        grade, level = "B+", "良好"
    elif overall_score >= 75:
        grade, level = "B", "中等偏上"
    elif overall_score >= 70:
        grade, level = "C+", "中等"
    elif overall_score >= 60:
        grade, level = "C", "及格"
    else:
        grade, level = "D", "需要努力"
    
    return {
        "score": overall_score,
        "grade": grade,
        "level": level,
        "segment_results": results,
        "video_frames": len(video_data),
        "user_frames": len(user_data),
        "best_match_info": {
            "video_start": main_result["video_start"],
            "user_start": main_result["user_start"],
            "segment_length": main_result["segment_length"]
        }
    }

def analyze_specific_segment(video_file: str, user_file: str, 
                           video_start: int, user_start: int, length: int = 10) -> Dict:
    """
    分析特定段落的详细相似度
    
    Args:
        video_file: 视频文件路径
        user_file: 用户文件路径
        video_start: 视频起始帧
        user_start: 用户起始帧
        length: 段落长度
    
    Returns:
        详细分析结果
    """
    video_data = load_dance_data(video_file)
    user_data = load_dance_data(user_file)
    
    if (video_start + length > len(video_data) or 
        user_start + length > len(user_data)):
        return {"error": "段落超出数据范围"}
    
    # 提取段落
    video_segment = video_data[video_start:video_start + length]
    user_segment = user_data[user_start:user_start + length]
    
    # 计算每帧的相似度
    frame_scores = []
    angle_names = ["左臂", "左前臂", "右臂", "右前臂", "左腿", "左小腿", "右腿", "右小腿"]
    
    for i in range(length):
        angle_diffs = angle_difference_vectorized(video_segment[i], user_segment[i])
        angle_similarities = np.maximum(0, 100 - (angle_diffs / 180) * 100)
        
        frame_info = {
            "frame": i,
            "overall_score": float(np.mean(angle_similarities)),
            "angle_scores": {
                angle_names[j]: float(angle_similarities[j]) 
                for j in range(8)
            },
            "angle_differences": {
                angle_names[j]: float(angle_diffs[j]) 
                for j in range(8)
            }
        }
        frame_scores.append(frame_info)
    
    overall_score = np.mean([frame["overall_score"] for frame in frame_scores])
    
    return {
        "overall_score": round(float(overall_score), 2),
        "frame_details": frame_scores,
        "segment_info": {
            "video_start": video_start,
            "user_start": user_start,
            "length": length
        }
    }

def print_quick_report(result: Dict):
    """打印快速分析报告"""
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
        return
    
    print("\n" + "="*50)
    print("🎭 快速舞蹈分析报告")
    print("="*50)
    print(f"📊 总体得分: {result['score']}/100")
    print(f"🏆 评级: {result['grade']} ({result['level']})")
    print(f"📋 数据: 视频{result['video_frames']}帧, 用户{result['user_frames']}帧")
    
    print(f"\n📈 分段分析:")
    for seg in result['segment_results']:
        print(f"   {seg['segment_type']}: {seg['score']}/100")
    
    best = result['best_match_info']
    print(f"\n🎯 最佳匹配: 视频第{best['video_start']}帧 vs 用户第{best['user_start']}帧 ({best['segment_length']}帧段落)")

# 使用示例
if __name__ == "__main__":
    print("🚀 快速舞蹈相似度分析")
    print("="*40)
    
    # 快速分析
    result = quick_dance_score()
    print_quick_report(result)
    
    # 如果分析成功，显示详细段落分析
    if "error" not in result and result['score'] > 0:
        print(f"\n🔍 详细段落分析 (前10帧):")
        best = result['best_match_info']
        detail = analyze_specific_segment(
            "video.json", "user.json", 
            best['video_start'], best['user_start'], 
            min(10, best['segment_length'])
        )
        
        if "error" not in detail:
            print(f"   段落总分: {detail['overall_score']}/100")
            for frame in detail['frame_details'][:3]:  # 只显示前3帧
                print(f"   第{frame['frame']+1}帧: {frame['overall_score']:.1f}/100")
                # 显示得分最低的角度
                worst_angle = min(frame['angle_scores'].items(), key=lambda x: x[1])
                print(f"      最需改进: {worst_angle[0]} ({worst_angle[1]:.1f}/100)")
