#!/usr/bin/env node

/**
 * 舞蹈动作相似度计算 - Node.js命令行工具
 * 使用方法: node danceScore.js [videoFile] [userFile]
 */

const fs = require('fs').promises;
const path = require('path');

class DanceSimilarityCalculator {
    constructor() {
        // 关键点权重
        this.pointWeights = [
            1.0,  // 鼻子
            1.2,  // 左眼
            1.2,  // 右眼  
            1.0,  // 左耳
            1.0,  // 右耳
            1.5,  // 左肩
            1.5,  // 右肩
            1.3,  // 左肘
            1.3,  // 右肘
            1.0,  // 左腕
            1.0,  // 右腕
            2.0,  // 左髋
            2.0,  // 右髋
            1.8,  // 左膝
            1.8,  // 右膝
            1.5,  // 左踝
            1.5   // 右踝
        ];
    }

    calculateDistance(point1, point2) {
        if (!point1 || !point2) return 1.0;
        
        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    calculateFrameSimilarity(videoFrame, userFrame) {
        if (!videoFrame || !userFrame) return 0;
        if (videoFrame.length !== userFrame.length) return 0;

        let totalWeightedScore = 0;
        let totalWeight = 0;
        const maxDistance = Math.sqrt(2);

        for (let i = 0; i < Math.min(videoFrame.length, this.pointWeights.length); i++) {
            const distance = this.calculateDistance(videoFrame[i], userFrame[i]);
            const similarity = Math.max(0, 100 * (1 - distance / maxDistance));
            
            const weight = this.pointWeights[i] || 1.0;
            totalWeightedScore += similarity * weight;
            totalWeight += weight;
        }

        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    }

    findBestMatchingSegment(videoData, userData, segmentLength = 10) {
        if (!videoData || !userData || videoData.length === 0 || userData.length === 0) {
            return { score: 0, videoStart: 0, userStart: 0 };
        }

        let bestScore = 0;
        let bestVideoStart = 0;
        let bestUserStart = 0;

        // 采样策略
        const maxFrames = 200;
        const videoStep = Math.max(1, Math.floor(videoData.length / maxFrames));
        const userStep = Math.max(1, Math.floor(userData.length / maxFrames));

        for (let userStart = 0; userStart < userData.length - segmentLength; userStart += userStep) {
            for (let videoStart = 0; videoStart < videoData.length - segmentLength; videoStart += videoStep) {
                let segmentScore = 0;
                let validFrames = 0;

                for (let i = 0; i < segmentLength; i++) {
                    if (userStart + i < userData.length && videoStart + i < videoData.length) {
                        const frameScore = this.calculateFrameSimilarity(
                            videoData[videoStart + i],
                            userData[userStart + i]
                        );
                        segmentScore += frameScore;
                        validFrames++;
                    }
                }

                if (validFrames > 0) {
                    const avgScore = segmentScore / validFrames;
                    if (avgScore > bestScore) {
                        bestScore = avgScore;
                        bestVideoStart = videoStart;
                        bestUserStart = userStart;
                    }
                }
            }
        }

        return {
            score: Math.round(bestScore * 100) / 100,
            videoStart: bestVideoStart,
            userStart: bestUserStart,
            segmentLength: segmentLength
        };
    }

    async quickScore(videoData, userData) {
        if (!videoData || !userData) {
            return { error: "数据无效", score: 0 };
        }

        console.log(`📊 数据信息: 视频${videoData.length}帧, 用户${userData.length}帧`);

        const segmentLengths = [5, 10, 15];
        const results = [];

        for (const length of segmentLengths) {
            if (length <= Math.min(videoData.length, userData.length)) {
                console.log(`🔄 计算${length}帧段落相似度...`);
                const result = this.findBestMatchingSegment(videoData, userData, length);
                result.segmentType = `${length}帧`;
                results.push(result);
            }
        }

        if (results.length === 0) {
            return { error: "无法计算相似度", score: 0 };
        }

        const mainResult = results.find(r => r.segmentType === "10帧") || results[results.length - 1];
        const overallScore = mainResult.score;

        let grade, level;
        if (overallScore >= 90) {
            grade = "A+"; level = "完美";
        } else if (overallScore >= 85) {
            grade = "A"; level = "优秀";
        } else if (overallScore >= 80) {
            grade = "B+"; level = "良好";
        } else if (overallScore >= 75) {
            grade = "B"; level = "中等偏上";
        } else if (overallScore >= 70) {
            grade = "C+"; level = "中等";
        } else if (overallScore >= 60) {
            grade = "C"; level = "及格";
        } else {
            grade = "D"; level = "需要努力";
        }

        return {
            score: overallScore,
            grade: grade,
            level: level,
            segmentResults: results,
            videoFrames: videoData.length,
            userFrames: userData.length,
            bestMatchInfo: {
                videoStart: mainResult.videoStart,
                userStart: mainResult.userStart,
                segmentLength: mainResult.segmentLength
            }
        };
    }
}

async function loadData(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        throw new Error(`无法加载文件 ${filePath}: ${error.message}`);
    }
}

function printReport(result) {
    if (result.error) {
        console.log(`❌ 错误: ${result.error}`);
        return;
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎭 舞蹈相似度分析报告');
    console.log('='.repeat(50));
    console.log(`📊 总体得分: ${result.score}/100`);
    console.log(`🏆 评级: ${result.grade} (${result.level})`);
    console.log(`📋 数据: 视频${result.videoFrames}帧, 用户${result.userFrames}帧`);
    
    console.log('\n📈 分段分析:');
    result.segmentResults.forEach(seg => {
        console.log(`   ${seg.segmentType}: ${seg.score}/100`);
    });
    
    const best = result.bestMatchInfo;
    console.log(`\n🎯 最佳匹配: 视频第${best.videoStart}帧 vs 用户第${best.userStart}帧 (${best.segmentLength}帧段落)`);
}

async function main() {
    const args = process.argv.slice(2);

    // 检查选项
    const isQuick = args.includes('--quick');

    // 过滤掉选项，只保留文件路径参数
    const fileArgs = args.filter(arg => !arg.startsWith('--'));

    // 默认文件路径
    const videoFile = fileArgs[0] || 'videoPoint.json';
    const userFile = fileArgs[1] || 'userPoint.json';

    try {
        console.log('🚀 开始舞蹈相似度分析...');
        
        // 加载数据
        const [videoData, userData] = await Promise.all([
            loadData(videoFile),
            loadData(userFile)
        ]);

        // 创建计算器并分析
        const calculator = new DanceSimilarityCalculator();
        const result = await calculator.quickScore(videoData, userData);

        if (isQuick) {
            console.log(`得分: ${result.score}/100 (${result.grade})`);
        } else {
            printReport(result);
        }

        process.exit(0);

    } catch (error) {
        console.error(`❌ 发生错误: ${error.message}`);
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
舞蹈动作相似度计算工具 - JavaScript版本

使用方法:
  node danceScore.js [videoFile] [userFile] [options]

参数:
  videoFile    标准视频数据文件路径 (默认: videoPoint.json)
  userFile     用户动作数据文件路径 (默认: userPoint.json)

选项:
  --quick      只显示得分，不显示详细报告
  --help       显示此帮助信息

示例:
  node danceScore.js                                    # 使用默认文件
  node danceScore.js videoPoint.json userPoint.json    # 指定文件
  node danceScore.js --quick                            # 快速得分
    `);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 运行主程序
if (require.main === module) {
    main().catch(error => {
        console.error(`❌ 未处理的错误: ${error.message}`);
        process.exit(1);
    });
}

module.exports = DanceSimilarityCalculator;
