<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>舞蹈动作实时对比分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .score-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .score-display {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .score-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 10px;
            min-width: 120px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .score-item.excellent {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
        }

        .score-item.good {
            background: linear-gradient(45deg, #5f27cd, #a55eea);
        }

        .score-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .comparison-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            height: 500px;
        }

        .pose-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .pose-title {
            text-align: center;
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #333;
            font-weight: bold;
        }

        .pose-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .timeline-container {
            margin-bottom: 20px;
        }

        .timeline-label {
            font-size: 1.2em;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }

        .timeline {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
            cursor: pointer;
        }

        .timeline::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }

        .timeline::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }

        .frame-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            font-size: 1.1em;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
            color: #666;
        }

        .error {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .comparison-container {
                flex-direction: column;
                height: auto;
            }
            
            .pose-canvas {
                height: 300px;
            }
            
            .score-display {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 舞蹈动作实时对比分析</h1>
            <p>拖动时间轴查看不同时刻的动作对比</p>
        </div>

        <div class="score-panel">
            <div class="score-display">
                <div class="score-item" id="currentScore">
                    <div class="score-value">--</div>
                    <div>当前帧得分</div>
                </div>
                <div class="score-item" id="overallScore">
                    <div class="score-value">--</div>
                    <div>总体得分</div>
                </div>
                <div class="score-item" id="gradeDisplay">
                    <div class="score-value">--</div>
                    <div>评级</div>
                </div>
            </div>
        </div>

        <div class="comparison-container">
            <div class="pose-panel">
                <div class="pose-title">📹 标准视频动作</div>
                <canvas class="pose-canvas" id="videoCanvas"></canvas>
            </div>
            <div class="pose-panel">
                <div class="pose-title">👤 用户动作</div>
                <canvas class="pose-canvas" id="userCanvas"></canvas>
            </div>
        </div>

        <div class="controls">
            <div class="timeline-container">
                <div class="timeline-label">时间轴控制</div>
                <input type="range" class="timeline" id="timelineSlider" min="0" max="100" value="0">
                <div class="frame-info">
                    <span>当前帧: <span id="currentFrame">0</span></span>
                    <span>总帧数: <span id="totalFrames">0</span></span>
                    <span>相似度: <span id="similarity">0%</span></span>
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="btn btn-primary" id="playBtn">▶️ 播放</button>
                <button class="btn btn-secondary" id="pauseBtn">⏸️ 暂停</button>
                <button class="btn btn-primary" id="resetBtn">🔄 重置</button>
                <button class="btn btn-secondary" id="analyzeBtn">📊 分析</button>
                <button class="btn btn-primary" id="toggleModeBtn">🎯 严格模式</button>
            </div>
        </div>

        <div id="loadingDiv" class="loading">
            🔄 正在加载数据...
        </div>

        <div id="errorDiv" class="error" style="display: none;"></div>
    </div>

    <script src="danceSimilarity.js"></script>
    <script src="strictDanceSimilarity.js"></script>
    <script>
        class DanceComparison {
            constructor() {
                this.videoData = null;
                this.userData = null;
                this.calculator = new DanceSimilarityCalculator();
                this.strictCalculator = new StrictDanceSimilarityCalculator();
                this.useStrictMode = true; // 默认使用严格模式
                this.currentFrame = 0;
                this.isPlaying = false;
                this.playInterval = null;
                this.overallResult = null;

                this.initializeElements();
                this.setupEventListeners();
                this.loadData();
            }

            initializeElements() {
                this.videoCanvas = document.getElementById('videoCanvas');
                this.userCanvas = document.getElementById('userCanvas');
                this.timelineSlider = document.getElementById('timelineSlider');
                this.currentFrameSpan = document.getElementById('currentFrame');
                this.totalFramesSpan = document.getElementById('totalFrames');
                this.similaritySpan = document.getElementById('similarity');
                this.loadingDiv = document.getElementById('loadingDiv');
                this.errorDiv = document.getElementById('errorDiv');
                
                // 设置canvas
                this.videoCtx = this.videoCanvas.getContext('2d');
                this.userCtx = this.userCanvas.getContext('2d');
                this.resizeCanvases();
            }

            resizeCanvases() {
                const rect = this.videoCanvas.getBoundingClientRect();
                this.videoCanvas.width = rect.width;
                this.videoCanvas.height = rect.height;
                this.userCanvas.width = rect.width;
                this.userCanvas.height = rect.height;
            }

            setupEventListeners() {
                this.timelineSlider.addEventListener('input', (e) => {
                    this.currentFrame = parseInt(e.target.value);
                    this.updateDisplay();
                });

                document.getElementById('playBtn').addEventListener('click', () => this.play());
                document.getElementById('pauseBtn').addEventListener('click', () => this.pause());
                document.getElementById('resetBtn').addEventListener('click', () => this.reset());
                document.getElementById('analyzeBtn').addEventListener('click', () => this.analyze());
                document.getElementById('toggleModeBtn').addEventListener('click', () => this.toggleMode());

                window.addEventListener('resize', () => this.resizeCanvases());
            }

            async loadData() {
                try {
                    this.showLoading(true);
                    
                    const [videoResponse, userResponse] = await Promise.all([
                        fetch('videoPoint.json'),
                        fetch('userPoint.json')
                    ]);

                    if (!videoResponse.ok || !userResponse.ok) {
                        throw new Error('无法加载数据文件');
                    }

                    this.videoData = await videoResponse.json();
                    this.userData = await userResponse.json();

                    console.log(`📊 数据加载完成: 视频${this.videoData.length}帧, 用户${this.userData.length}帧`);

                    this.initializeTimeline();
                    this.updateDisplay();
                    this.showLoading(false);
                    
                    // 自动进行整体分析
                    setTimeout(() => this.analyze(), 1000);

                } catch (error) {
                    this.showError(`数据加载失败: ${error.message}`);
                    this.showLoading(false);
                }
            }

            initializeTimeline() {
                const maxFrames = Math.min(this.videoData.length, this.userData.length);
                this.timelineSlider.max = maxFrames - 1;
                this.totalFramesSpan.textContent = maxFrames;
            }

            updateDisplay() {
                if (!this.videoData || !this.userData) return;

                const maxFrames = Math.min(this.videoData.length, this.userData.length);
                if (this.currentFrame >= maxFrames) {
                    this.currentFrame = maxFrames - 1;
                }

                this.currentFrameSpan.textContent = this.currentFrame + 1;
                this.timelineSlider.value = this.currentFrame;

                // 绘制姿态
                this.drawPose(this.videoCtx, this.videoData[this.currentFrame], '#4CAF50', '标准');
                this.drawPose(this.userCtx, this.userData[this.currentFrame], '#2196F3', '用户');

                // 计算当前帧相似度 (使用严格模式)
                const similarity = this.useStrictMode ?
                    this.strictCalculator.calculateStrictFrameSimilarity(
                        this.videoData[this.currentFrame],
                        this.userData[this.currentFrame]
                    ) :
                    this.calculator.calculateFrameSimilarity(
                        this.videoData[this.currentFrame],
                        this.userData[this.currentFrame]
                    );

                this.similaritySpan.textContent = `${Math.round(similarity)}%`;
                this.updateCurrentScore(similarity);

                // 添加详细分析信息
                this.showDetailedAnalysis(similarity);
            }

            drawPose(ctx, points, color, label) {
                if (!points || points.length === 0) return;

                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

                const width = ctx.canvas.width;
                const height = ctx.canvas.height;

                // 绘制标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(label, 10, 25);

                // 归一化姿态用于显示
                const normalizedPoints = this.calculator.normalizePose(points);

                // 计算显示范围
                let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
                normalizedPoints.forEach(point => {
                    if (point && point.x !== undefined && point.y !== undefined) {
                        minX = Math.min(minX, point.x);
                        maxX = Math.max(maxX, point.x);
                        minY = Math.min(minY, point.y);
                        maxY = Math.max(maxY, point.y);
                    }
                });

                const rangeX = maxX - minX || 1;
                const rangeY = maxY - minY || 1;
                const scale = Math.min(width * 0.8 / rangeX, height * 0.8 / rangeY);
                const offsetX = width / 2 - (minX + maxX) / 2 * scale;
                const offsetY = height / 2 - (minY + maxY) / 2 * scale;

                // 绘制关键点
                ctx.fillStyle = color;
                normalizedPoints.forEach((point, index) => {
                    if (point && point.x !== undefined && point.y !== undefined) {
                        const x = point.x * scale + offsetX;
                        const y = point.y * scale + offsetY;

                        ctx.beginPath();
                        ctx.arc(x, y, 4, 0, 2 * Math.PI);
                        ctx.fill();

                        // 重要关键点显示标号
                        if ([5, 6, 11, 12, 13, 14].includes(index)) {
                            ctx.fillStyle = '#333';
                            ctx.font = '10px Arial';
                            ctx.fillText(index.toString(), x + 6, y - 6);
                            ctx.fillStyle = color;
                        }
                    }
                });

                // 绘制骨架连接线
                this.drawSkeleton(ctx, normalizedPoints, scale, offsetX, offsetY, color);
            }

            drawSkeleton(ctx, points, scale, offsetX, offsetY, color) {
                if (!points || points.length < 17) return;

                ctx.strokeStyle = color;
                ctx.lineWidth = 2;

                // 定义骨架连接关系
                const connections = [
                    [0, 1], [0, 2], [1, 3], [2, 4], // 头部
                    [5, 6], [5, 7], [7, 9], [6, 8], [8, 10], // 上身
                    [5, 11], [6, 12], [11, 12], // 躯干
                    [11, 13], [13, 15], [12, 14], [14, 16] // 下身
                ];

                connections.forEach(([start, end]) => {
                    if (points[start] && points[end] &&
                        points[start].x !== undefined && points[end].x !== undefined) {

                        ctx.beginPath();
                        ctx.moveTo(
                            points[start].x * scale + offsetX,
                            points[start].y * scale + offsetY
                        );
                        ctx.lineTo(
                            points[end].x * scale + offsetX,
                            points[end].y * scale + offsetY
                        );
                        ctx.stroke();
                    }
                });
            }

            showDetailedAnalysis(similarity) {
                // 在控制台显示详细分析
                if (this.currentFrame % 30 === 0) { // 每30帧显示一次详细信息
                    const mode = this.useStrictMode ? '严格模式' : '标准模式';
                    console.log(`帧 ${this.currentFrame + 1}: 相似度 ${similarity.toFixed(2)}/100 (${mode})`);
                }
            }

            toggleMode() {
                this.useStrictMode = !this.useStrictMode;
                const btn = document.getElementById('toggleModeBtn');

                if (this.useStrictMode) {
                    btn.textContent = '🎯 严格模式';
                    btn.className = 'btn btn-primary';
                } else {
                    btn.textContent = '📊 标准模式';
                    btn.className = 'btn btn-secondary';
                }

                // 重新计算当前帧
                this.updateDisplay();

                // 显示切换提示
                console.log(`已切换到${this.useStrictMode ? '严格' : '标准'}模式`);
            }

            updateCurrentScore(score) {
                const scoreElement = document.getElementById('currentScore');
                const scoreValue = scoreElement.querySelector('.score-value');
                scoreValue.textContent = Math.round(score);

                // 根据分数更新样式
                scoreElement.className = 'score-item';
                if (score >= 85) {
                    scoreElement.classList.add('excellent');
                } else if (score >= 70) {
                    scoreElement.classList.add('good');
                }
            }

            play() {
                if (this.isPlaying) return;
                
                this.isPlaying = true;
                this.playInterval = setInterval(() => {
                    const maxFrames = Math.min(this.videoData.length, this.userData.length);
                    this.currentFrame = (this.currentFrame + 1) % maxFrames;
                    this.updateDisplay();
                }, 100); // 10fps
            }

            pause() {
                this.isPlaying = false;
                if (this.playInterval) {
                    clearInterval(this.playInterval);
                    this.playInterval = null;
                }
            }

            reset() {
                this.pause();
                this.currentFrame = 0;
                this.updateDisplay();
            }

            async analyze() {
                if (!this.videoData || !this.userData) return;

                this.showLoading(true, '正在分析整体相似度...');

                try {
                    // 使用严格算法进行分析
                    this.overallResult = this.useStrictMode ?
                        await this.strictCalculator.strictQuickScore(this.videoData, this.userData) :
                        await this.calculator.quickScore(this.videoData, this.userData);

                    if (this.overallResult.error) {
                        this.showError(this.overallResult.error);
                    } else {
                        this.updateOverallScore(this.overallResult);
                        console.log('分析结果 (严格模式):', this.overallResult);
                    }
                } catch (error) {
                    this.showError(`分析失败: ${error.message}`);
                }

                this.showLoading(false);
            }

            updateOverallScore(result) {
                const overallElement = document.getElementById('overallScore');
                const gradeElement = document.getElementById('gradeDisplay');

                overallElement.querySelector('.score-value').textContent = Math.round(result.score);
                gradeElement.querySelector('.score-value').textContent = result.grade;

                // 更新样式
                overallElement.className = 'score-item';
                gradeElement.className = 'score-item';
                
                if (result.score >= 85) {
                    overallElement.classList.add('excellent');
                    gradeElement.classList.add('excellent');
                } else if (result.score >= 70) {
                    overallElement.classList.add('good');
                    gradeElement.classList.add('good');
                }
            }

            showLoading(show, message = '正在加载数据...') {
                this.loadingDiv.style.display = show ? 'block' : 'none';
                if (show) {
                    this.loadingDiv.textContent = `🔄 ${message}`;
                }
            }

            showError(message) {
                this.errorDiv.textContent = `❌ ${message}`;
                this.errorDiv.style.display = 'block';
                setTimeout(() => {
                    this.errorDiv.style.display = 'none';
                }, 5000);
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new DanceComparison();
        });
    </script>
</body>
</html>
