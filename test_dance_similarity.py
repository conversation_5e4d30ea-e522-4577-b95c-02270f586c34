#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
舞蹈相似度计算测试脚本
测试dance_similarity.py和simple_dance_scorer.py的功能
"""

import sys
import os

# 确保可以导入我们的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from dance_similarity import DanceSimilarityCalculator
    from simple_dance_scorer import analyze_dance_performance, quick_score, print_analysis_report
    print("✅ 成功导入所有模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_basic_functionality():
    """测试基本功能"""
    print("\n" + "="*50)
    print("🧪 测试基本功能")
    print("="*50)
    
    # 测试简单评分器
    print("\n1. 测试简单评分器...")
    try:
        score = quick_score()
        print(f"   快速得分: {score}/100")
        
        if score > 0:
            print("   ✅ 简单评分器工作正常")
        else:
            print("   ⚠️  得分为0，可能数据文件有问题")
            
    except Exception as e:
        print(f"   ❌ 简单评分器测试失败: {e}")
    
    # 测试详细分析
    print("\n2. 测试详细分析...")
    try:
        analysis = analyze_dance_performance()
        if "error" not in analysis:
            print("   ✅ 详细分析工作正常")
            print(f"   总体得分: {analysis['overall_score']}")
            print(f"   评级: {analysis['grade']}")
        else:
            print(f"   ❌ 详细分析失败: {analysis['error']}")
            
    except Exception as e:
        print(f"   ❌ 详细分析测试失败: {e}")
    
    # 测试高级计算器
    print("\n3. 测试高级计算器...")
    try:
        calculator = DanceSimilarityCalculator()
        result = calculator.calculate_overall_similarity(window_size=5, step_size=3)
        
        if "error" not in result:
            print("   ✅ 高级计算器工作正常")
            print(f"   总体得分: {result['overall_score']}")
            print(f"   表现等级: {result['performance_level']}")
        else:
            print(f"   ❌ 高级计算器失败: {result['error']}")
            
    except Exception as e:
        print(f"   ❌ 高级计算器测试失败: {e}")

def test_data_files():
    """测试数据文件"""
    print("\n" + "="*50)
    print("📁 测试数据文件")
    print("="*50)
    
    files_to_check = ["video.json", "user.json"]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            try:
                import json
                with open(file_name, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   ✅ {file_name}: {len(data)} 帧数据")
                
                if len(data) > 0 and len(data[0]) == 8:
                    print(f"      每帧包含8个角度数据 ✓")
                else:
                    print(f"      ⚠️  数据格式可能有问题")
                    
            except Exception as e:
                print(f"   ❌ {file_name} 读取失败: {e}")
        else:
            print(f"   ❌ {file_name} 文件不存在")

def run_full_analysis():
    """运行完整分析"""
    print("\n" + "="*50)
    print("🎭 运行完整舞蹈分析")
    print("="*50)
    
    try:
        # 使用简单评分器进行分析
        analysis = analyze_dance_performance()
        print_analysis_report(analysis)
        
    except Exception as e:
        print(f"❌ 完整分析失败: {e}")

def create_sample_data():
    """创建示例数据用于测试"""
    print("\n" + "="*50)
    print("📝 创建示例数据")
    print("="*50)
    
    import json
    import random
    
    # 检查是否已有数据文件
    if os.path.exists("video.json") and os.path.exists("user.json"):
        print("   数据文件已存在，跳过创建示例数据")
        return
    
    print("   创建示例数据用于测试...")
    
    # 创建示例视频数据 (100帧)
    video_data = []
    for i in range(100):
        frame = [
            90 + random.uniform(-10, 10),   # 左臂角度
            180 + random.uniform(-15, 15),  # 左前臂角度
            270 + random.uniform(-10, 10),  # 右臂角度
            180 + random.uniform(-15, 15),  # 右前臂角度
            90 + random.uniform(-20, 20),   # 左腿角度
            180 + random.uniform(-25, 25),  # 左小腿角度
            270 + random.uniform(-20, 20),  # 右腿角度
            180 + random.uniform(-25, 25)   # 右小腿角度
        ]
        video_data.append(frame)
    
    # 创建示例用户数据 (80帧，基于视频数据但有一些差异)
    user_data = []
    for i in range(80):
        video_frame = video_data[i % len(video_data)]
        frame = [
            video_frame[j] + random.uniform(-5, 5) for j in range(8)
        ]
        user_data.append(frame)
    
    # 保存示例数据
    try:
        with open("sample_video.json", 'w', encoding='utf-8') as f:
            json.dump(video_data, f, indent=2)
        
        with open("sample_user.json", 'w', encoding='utf-8') as f:
            json.dump(user_data, f, indent=2)
        
        print("   ✅ 示例数据创建成功:")
        print("      - sample_video.json (100帧)")
        print("      - sample_user.json (80帧)")
        print("   💡 可以使用这些文件测试功能")
        
    except Exception as e:
        print(f"   ❌ 创建示例数据失败: {e}")

def main():
    """主测试函数"""
    print("🎯 舞蹈相似度计算器测试程序")
    print("="*60)
    
    # 测试数据文件
    test_data_files()
    
    # 如果没有数据文件，创建示例数据
    if not (os.path.exists("video.json") and os.path.exists("user.json")):
        create_sample_data()
        print("\n💡 提示: 请将真实的video.json和user.json文件放在当前目录下进行测试")
    
    # 测试基本功能
    test_basic_functionality()
    
    # 如果有数据文件，运行完整分析
    if os.path.exists("video.json") and os.path.exists("user.json"):
        run_full_analysis()
    elif os.path.exists("sample_video.json") and os.path.exists("sample_user.json"):
        print("\n🧪 使用示例数据进行测试...")
        try:
            analysis = analyze_dance_performance("sample_video.json", "sample_user.json")
            print_analysis_report(analysis)
        except Exception as e:
            print(f"❌ 示例数据测试失败: {e}")
    
    print("\n" + "="*60)
    print("✅ 测试完成!")
    print("="*60)

if __name__ == "__main__":
    main()
