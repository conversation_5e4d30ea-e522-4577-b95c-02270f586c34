# 舞蹈动作相似度计算器 - JavaScript版本

## 🎯 功能概述

这是一个用JavaScript重写的舞蹈动作相似度计算工具，支持：
- 关键点坐标数据的相似度分析
- 实时可视化对比界面
- 命令行工具
- 浏览器端和Node.js端双重支持

## 📁 文件结构

```
aidance/
├── videoPoint.json           # 标准舞蹈视频关键点数据
├── userPoint.json            # 用户舞蹈关键点数据
├── dance_comparison.html     # 实时对比可视化界面 ⭐
├── danceSimilarity.js        # 核心相似度计算类
├── danceScore.js            # Node.js命令行工具
└── JavaScript使用说明.md     # 本文档
```

## 🚀 使用方法

### 方法1: 实时可视化界面 (推荐)

1. **直接打开HTML文件**
   ```bash
   # 在浏览器中打开
   open dance_comparison.html
   # 或双击文件
   ```

2. **功能特点**
   - 🎭 左右对比显示标准动作和用户动作
   - 📊 实时显示当前帧得分和总体得分
   - 🎚️ 可拖动时间轴查看不同时刻
   - ▶️ 支持播放/暂停/重置
   - 📈 自动分析整体相似度

3. **界面说明**
   - **得分面板**: 显示当前帧得分、总体得分、评级
   - **对比面板**: 左侧标准视频，右侧用户动作
   - **时间轴**: 拖动查看不同帧，显示帧信息和相似度
   - **控制按钮**: 播放、暂停、重置、分析

### 方法2: Node.js命令行工具

1. **基本使用**
   ```bash
   # 使用默认文件
   node danceScore.js
   
   # 指定文件
   node danceScore.js videoPoint.json userPoint.json
   
   # 快速得分
   node danceScore.js --quick
   
   # 显示帮助
   node danceScore.js --help
   ```

2. **示例输出**
   ```
   🚀 开始舞蹈相似度分析...
   📊 数据信息: 视频8244帧, 用户8300帧
   🔄 计算5帧段落相似度...
   🔄 计算10帧段落相似度...
   🔄 计算15帧段落相似度...
   
   ==================================================
   🎭 舞蹈相似度分析报告
   ==================================================
   📊 总体得分: 85.6/100
   🏆 评级: A (优秀)
   📋 数据: 视频8244帧, 用户8300帧
   
   📈 分段分析:
      5帧: 87.2/100
      10帧: 85.6/100
      15帧: 84.1/100
   
   🎯 最佳匹配: 视频第1200帧 vs 用户第1150帧 (10帧段落)
   ```

### 方法3: JavaScript代码调用

```javascript
// 在浏览器中
const calculator = new DanceSimilarityCalculator();

// 加载数据
fetch('videoPoint.json').then(r => r.json()).then(videoData => {
    fetch('userPoint.json').then(r => r.json()).then(userData => {
        // 计算相似度
        calculator.quickScore(videoData, userData).then(result => {
            console.log(`得分: ${result.score}/100`);
            console.log(`评级: ${result.grade}`);
        });
    });
});

// 在Node.js中
const DanceSimilarityCalculator = require('./danceSimilarity.js');
const fs = require('fs').promises;

async function analyze() {
    const calculator = new DanceSimilarityCalculator();
    
    const videoData = JSON.parse(await fs.readFile('videoPoint.json', 'utf8'));
    const userData = JSON.parse(await fs.readFile('userPoint.json', 'utf8'));
    
    const result = await calculator.quickScore(videoData, userData);
    console.log(`得分: ${result.score}/100`);
}
```

## 📊 数据格式说明

### 关键点坐标格式
```json
[
    [  // 第一帧
        {"x": 0.509, "y": 0.499},  // 关键点0: 鼻子
        {"x": 0.523, "y": 0.495},  // 关键点1: 左眼
        {"x": 0.493, "y": 0.495},  // 关键点2: 右眼
        // ... 更多关键点
    ],
    [  // 第二帧
        // ... 同样格式的关键点
    ]
]
```

### 17个关键点说明
```
0: 鼻子      5: 左肩      11: 左髋
1: 左眼      6: 右肩      12: 右髋  
2: 右眼      7: 左肘      13: 左膝
3: 左耳      8: 右肘      14: 右膝
4: 右耳      9: 左腕      15: 左踝
            10: 右腕      16: 右踝
```

## 🎨 可视化界面特点

### 实时对比显示
- **骨架绘制**: 自动连接关键点形成人体骨架
- **颜色区分**: 标准动作(绿色)，用户动作(蓝色)
- **关键点标号**: 显示每个关键点的编号
- **实时更新**: 拖动时间轴实时更新画面

### 得分系统
- **当前帧得分**: 实时显示当前帧的相似度
- **总体得分**: 整体分析结果
- **评级显示**: A+/A/B+/B/C+/C/D 七级评分
- **颜色编码**: 优秀(蓝色)，良好(紫色)，一般(红色)

### 交互控制
- **时间轴拖拽**: 精确控制查看帧
- **播放控制**: 10fps自动播放
- **分析按钮**: 触发整体相似度分析
- **响应式设计**: 支持移动端查看

## 🔧 算法原理

### 1. 距离计算
```javascript
calculateDistance(point1, point2) {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    return Math.sqrt(dx * dx + dy * dy);
}
```

### 2. 相似度转换
```javascript
const similarity = Math.max(0, 100 * (1 - distance / maxDistance));
```

### 3. 加权计算
```javascript
// 不同关键点的权重
const weights = [
    1.0,  // 鼻子
    1.2,  // 眼部
    1.5,  // 肩部
    1.3,  // 肘部
    2.0,  // 髋部 (重要)
    1.8,  // 膝部 (重要)
    1.5   // 踝部
];
```

### 4. 最佳匹配
使用滑动窗口在视频数据中寻找与用户动作最匹配的段落。

## 📈 性能优化

### 数据采样
- 大数据集自动采样到200帧以内
- 保持数据代表性的同时提升计算速度

### 渲染优化
- Canvas硬件加速
- 按需重绘
- 响应式布局

## 🎯 评分标准

- **A+ (90-100分)**: 完美 - 动作高度一致
- **A (85-89分)**: 优秀 - 动作很好
- **B+ (80-84分)**: 良好 - 基本正确
- **B (75-79分)**: 中等偏上 - 需要改进
- **C+ (70-74分)**: 中等 - 需要练习
- **C (60-69分)**: 及格 - 有待提高
- **D (0-59分)**: 需要努力 - 建议重新学习

## 🚀 快速开始

1. **确保数据文件存在**
   ```
   videoPoint.json - 标准视频关键点数据
   userPoint.json  - 用户动作关键点数据
   ```

2. **打开可视化界面**
   ```bash
   # 直接在浏览器中打开
   dance_comparison.html
   ```

3. **或使用命令行**
   ```bash
   node danceScore.js --quick
   ```

## 🔍 故障排除

### 常见问题
1. **数据加载失败**: 检查JSON文件格式和路径
2. **画面不显示**: 确保关键点数据完整
3. **得分异常**: 检查数据中是否有无效坐标

### 调试方法
```javascript
// 在浏览器控制台查看数据
console.log('视频数据:', videoData[0]);
console.log('用户数据:', userData[0]);
```

## 🎉 特色功能

- ✅ **双端支持**: 浏览器和Node.js
- ✅ **实时可视化**: 直观的动作对比
- ✅ **高性能**: 优化的算法和渲染
- ✅ **易于使用**: 简单的API和界面
- ✅ **响应式**: 支持各种屏幕尺寸

立即打开 `dance_comparison.html` 开始体验吧！🎭
