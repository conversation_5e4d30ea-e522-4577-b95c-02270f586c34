# 舞蹈动作相似度计算器 - 使用说明

## 🎯 功能概述

这个工具可以计算用户舞蹈动作与标准视频动作的相似度，给出0-100分的评分。

根据你提供的数据分析结果：**你的舞蹈得分是 91.82/100，评级为 A+ (完美)！** 🎉

## 📁 文件结构

```
aidance/
├── video.json              # 标准舞蹈视频数据 (868帧)
├── user.json               # 用户舞蹈数据 (781帧)
├── dance_score.py          # 命令行工具 (推荐使用)
├── fast_dance_scorer.py    # 快速评分器
├── simple_dance_scorer.py  # 简单评分器
├── dance_similarity.py     # 高级分析器
├── test_dance_similarity.py # 测试脚本
└── README.md               # 详细文档
```

## 🚀 最简单的使用方法

### 1. 获取总体得分
```bash
python dance_score.py --quick
```
输出：`得分: 91.82/100 (A+)`

### 2. 查看详细报告
```bash
python dance_score.py
```

### 3. 分析特定段落
```bash
python dance_score.py --detail 470 584 5
```

## 📊 你的分析结果

### 总体表现
- **得分**: 91.82/100
- **评级**: A+ (完美)
- **数据量**: 视频868帧，用户781帧

### 分段表现
- **5帧段落**: 94.72/100 (优秀)
- **10帧段落**: 91.82/100 (完美)
- **15帧段落**: 89.99/100 (优秀)

### 最佳匹配位置
- 视频第470帧 vs 用户第584帧
- 匹配段落长度：10帧

### 需要改进的地方
根据详细分析，**左前臂动作**是需要重点关注的部分：
- 左前臂角度差值约80-88度
- 得分约50-55/100
- 建议重点练习左前臂的动作协调

## 🎭 8个角度说明

你的数据包含8个身体角度：

1. **左臂角度** - RIGHT_SHOULDER → LEFT_SHOULDER → LEFT_ELBOW
2. **左前臂角度** - LEFT_SHOULDER → LEFT_ELBOW → LEFT_WRIST ⚠️ (需改进)
3. **右臂角度** - LEFT_SHOULDER → RIGHT_SHOULDER → RIGHT_ELBOW  
4. **右前臂角度** - RIGHT_SHOULDER → RIGHT_ELBOW → RIGHT_WRIST
5. **左腿角度** - RIGHT_HIP → LEFT_HIP → LEFT_KNEE
6. **左小腿角度** - LEFT_HIP → LEFT_KNEE → LEFT_ANKLE ✅ (表现最好)
7. **右腿角度** - LEFT_HIP → RIGHT_HIP → RIGHT_KNEE
8. **右小腿角度** - RIGHT_HIP → RIGHT_KNEE → RIGHT_ANKLE

## 💡 改进建议

基于你91.82分的优秀表现，建议：

1. **继续保持**: 腿部动作非常标准，特别是左小腿动作
2. **重点改进**: 左前臂的动作协调性
3. **细节完善**: 虽然总体很好，但可以追求更高的一致性

## 🔧 高级使用

### Python代码调用
```python
from fast_dance_scorer import quick_dance_score, print_quick_report

# 获取详细结果
result = quick_dance_score()
print(f"你的得分: {result['score']}/100")
print(f"评级: {result['grade']}")

# 打印完整报告
print_quick_report(result)
```

### 分析其他数据文件
```bash
python dance_score.py my_video.json my_user.json
```

### 批量分析
```python
from fast_dance_scorer import quick_dance_score

files = [
    ("video1.json", "user1.json"),
    ("video2.json", "user2.json"),
]

for video, user in files:
    result = quick_dance_score(video, user)
    print(f"{user}: {result['score']}/100")
```

## 🏆 评级系统

- **A+ (90-100分)**: 完美 ← **你在这里！**
- **A (85-89分)**: 优秀
- **B+ (80-84分)**: 良好
- **B (75-79分)**: 中等偏上
- **C+ (70-74分)**: 中等
- **C (60-69分)**: 及格
- **D (0-59分)**: 需要努力

## 📞 技术支持

如果遇到问题：

1. 检查JSON文件格式是否正确
2. 确保每帧包含8个角度值
3. 运行测试脚本：`python test_dance_similarity.py`
4. 查看详细文档：`README.md`

## 🎉 恭喜！

你的舞蹈表现非常出色，91.82分的成绩说明你的动作与标准视频高度相似。继续保持，并重点改进左前臂的动作协调性，你就能达到更完美的表现！
