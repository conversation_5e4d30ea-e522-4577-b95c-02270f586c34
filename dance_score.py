#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
舞蹈动作相似度计算命令行工具
使用方法: python dance_score.py [video_file] [user_file]
"""

import sys
import argparse
from fast_dance_scorer import quick_dance_score, print_quick_report, analyze_specific_segment

def main():
    parser = argparse.ArgumentParser(
        description="舞蹈动作相似度计算工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python dance_score.py                           # 使用默认文件 video.json 和 user.json
  python dance_score.py video.json user.json     # 指定文件
  python dance_score.py --detail 100 50 10       # 详细分析特定段落
        """
    )
    
    parser.add_argument(
        'video_file', 
        nargs='?', 
        default='video.json',
        help='标准视频数据文件路径 (默认: video.json)'
    )
    
    parser.add_argument(
        'user_file', 
        nargs='?', 
        default='user.json',
        help='用户动作数据文件路径 (默认: user.json)'
    )
    
    parser.add_argument(
        '--detail', 
        nargs=3, 
        metavar=('VIDEO_START', 'USER_START', 'LENGTH'),
        type=int,
        help='详细分析特定段落: 视频起始帧 用户起始帧 段落长度'
    )
    
    parser.add_argument(
        '--quick', 
        action='store_true',
        help='只显示得分，不显示详细报告'
    )
    
    args = parser.parse_args()
    
    try:
        if args.detail:
            # 详细段落分析
            video_start, user_start, length = args.detail
            print(f"🔍 分析段落: 视频第{video_start}帧, 用户第{user_start}帧, 长度{length}帧")
            
            result = analyze_specific_segment(
                args.video_file, args.user_file, 
                video_start, user_start, length
            )
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
                return 1
            
            print(f"\n📊 段落总分: {result['overall_score']}/100")
            print(f"📋 段落信息: 视频帧{video_start}-{video_start+length-1}, 用户帧{user_start}-{user_start+length-1}")
            
            print(f"\n📈 逐帧分析:")
            for frame in result['frame_details']:
                print(f"  第{frame['frame']+1}帧: {frame['overall_score']:.1f}/100")
                
                # 显示最好和最差的角度
                angles = frame['angle_scores']
                best_angle = max(angles.items(), key=lambda x: x[1])
                worst_angle = min(angles.items(), key=lambda x: x[1])
                
                print(f"    最佳: {best_angle[0]} ({best_angle[1]:.1f}/100)")
                print(f"    最差: {worst_angle[0]} ({worst_angle[1]:.1f}/100, 差值{frame['angle_differences'][worst_angle[0]]:.1f}°)")
        
        else:
            # 快速分析
            print("🚀 开始舞蹈相似度分析...")
            result = quick_dance_score(args.video_file, args.user_file)
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
                return 1
            
            if args.quick:
                # 只显示得分
                print(f"得分: {result['score']}/100 ({result['grade']})")
            else:
                # 显示完整报告
                print_quick_report(result)
                
                # 提供进一步分析的建议
                best = result['best_match_info']
                print(f"\n💡 提示: 可以使用以下命令查看详细分析:")
                print(f"python dance_score.py --detail {best['video_start']} {best['user_start']} {best['segment_length']}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️  分析被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
