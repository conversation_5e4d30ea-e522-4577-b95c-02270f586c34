import json
import math
import numpy as np
from typing import List, Tu<PERSON>, Dict

class DanceSimilarityCalculator:
    """
    舞蹈动作相似度计算器
    用于计算用户舞蹈动作与标准视频动作的相似度，并给出0-100分的评分
    """
    
    def __init__(self, video_data_path: str = "video.json", user_data_path: str = "user.json"):
        """
        初始化计算器
        
        Args:
            video_data_path: 标准视频数据文件路径
            user_data_path: 用户舞蹈数据文件路径
        """
        self.video_data = self.load_data(video_data_path)
        self.user_data = self.load_data(user_data_path)
        
        # 8个角度对应的权重，可以根据重要性调整
        self.angle_weights = [
            1.2,  # RIGHT_SHOULDER, LEFT_SHOULDER, LEFT_ELBOW - 左臂动作
            1.0,  # LEFT_SHOULDER, LEFT_ELBOW, LEFT_WRIST - 左前臂动作
            1.2,  # LEFT_SHOULDER, RIGHT_SHOULDER, RIGHT_ELBOW - 右臂动作
            1.0,  # RIGHT_SHOULDER, RIGHT_ELBOW, RIGHT_WRIST - 右前臂动作
            1.5,  # RIGHT_HIP, LEFT_HIP, LEFT_KNEE - 左腿动作
            1.3,  # LEFT_HIP, LEFT_KNEE, LEFT_ANKLE - 左小腿动作
            1.5,  # LEFT_HIP, RIGHT_HIP, RIGHT_KNEE - 右腿动作
            1.3   # RIGHT_HIP, RIGHT_KNEE, RIGHT_ANKLE - 右小腿动作
        ]
    
    def load_data(self, file_path: str) -> List[List[float]]:
        """
        加载JSON数据文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            角度数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except FileNotFoundError:
            print(f"文件 {file_path} 未找到")
            return []
        except json.JSONDecodeError:
            print(f"文件 {file_path} JSON格式错误")
            return []
    
    def normalize_angle(self, angle: float) -> float:
        """
        将角度标准化到0-360度范围
        
        Args:
            angle: 输入角度
            
        Returns:
            标准化后的角度
        """
        return angle % 360
    
    def angle_difference(self, angle1: float, angle2: float) -> float:
        """
        计算两个角度之间的最小差值
        
        Args:
            angle1: 角度1
            angle2: 角度2
            
        Returns:
            角度差值 (0-180度)
        """
        angle1 = self.normalize_angle(angle1)
        angle2 = self.normalize_angle(angle2)
        
        diff = abs(angle1 - angle2)
        return min(diff, 360 - diff)
    
    def calculate_frame_similarity(self, video_frame: List[float], user_frame: List[float]) -> float:
        """
        计算单帧动作的相似度
        
        Args:
            video_frame: 标准视频帧的8个角度
            user_frame: 用户动作帧的8个角度
            
        Returns:
            相似度分数 (0-100)
        """
        if len(video_frame) != 8 or len(user_frame) != 8:
            return 0.0
        
        total_weighted_score = 0.0
        total_weight = sum(self.angle_weights)
        
        for i in range(8):
            # 计算角度差值
            angle_diff = self.angle_difference(video_frame[i], user_frame[i])
            
            # 将角度差值转换为相似度分数 (0-100)
            # 角度差值越小，相似度越高
            similarity = max(0, 100 - (angle_diff / 180) * 100)
            
            # 应用权重
            weighted_score = similarity * self.angle_weights[i]
            total_weighted_score += weighted_score
        
        return total_weighted_score / total_weight
    
    def find_best_match_window(self, start_frame: int, window_size: int = 10) -> Tuple[float, int]:
        """
        在指定窗口内找到最佳匹配的帧序列
        
        Args:
            start_frame: 用户数据的起始帧
            window_size: 匹配窗口大小
            
        Returns:
            (最佳相似度分数, 最佳匹配的视频起始帧)
        """
        if start_frame + window_size > len(self.user_data):
            return 0.0, 0
        
        best_score = 0.0
        best_video_start = 0
        
        # 在视频数据中寻找最佳匹配位置
        for video_start in range(len(self.video_data) - window_size + 1):
            total_score = 0.0
            
            for i in range(window_size):
                if video_start + i < len(self.video_data) and start_frame + i < len(self.user_data):
                    frame_score = self.calculate_frame_similarity(
                        self.video_data[video_start + i],
                        self.user_data[start_frame + i]
                    )
                    total_score += frame_score
            
            avg_score = total_score / window_size
            if avg_score > best_score:
                best_score = avg_score
                best_video_start = video_start
        
        return best_score, best_video_start
    
    def calculate_overall_similarity(self, window_size: int = 10, step_size: int = 5) -> Dict:
        """
        计算整体舞蹈相似度
        
        Args:
            window_size: 分析窗口大小
            step_size: 窗口移动步长
            
        Returns:
            包含详细分析结果的字典
        """
        if not self.video_data or not self.user_data:
            return {
                "overall_score": 0.0,
                "error": "数据加载失败"
            }
        
        scores = []
        frame_details = []
        
        # 滑动窗口分析
        for start_frame in range(0, len(self.user_data) - window_size + 1, step_size):
            score, best_match_start = self.find_best_match_window(start_frame, window_size)
            scores.append(score)
            
            frame_details.append({
                "user_start_frame": start_frame,
                "user_end_frame": start_frame + window_size - 1,
                "video_match_start": best_match_start,
                "video_match_end": best_match_start + window_size - 1,
                "similarity_score": round(score, 2)
            })
        
        if not scores:
            return {
                "overall_score": 0.0,
                "error": "无法计算相似度"
            }
        
        # 计算总体分数
        overall_score = np.mean(scores)
        
        # 分析结果
        result = {
            "overall_score": round(overall_score, 2),
            "max_score": round(max(scores), 2),
            "min_score": round(min(scores), 2),
            "score_std": round(np.std(scores), 2),
            "total_frames_analyzed": len(scores),
            "frame_details": frame_details,
            "performance_level": self.get_performance_level(overall_score),
            "suggestions": self.get_suggestions(overall_score, scores)
        }
        
        return result
    
    def get_performance_level(self, score: float) -> str:
        """
        根据分数获取表现等级
        
        Args:
            score: 相似度分数
            
        Returns:
            表现等级描述
        """
        if score >= 90:
            return "完美 (Perfect)"
        elif score >= 80:
            return "优秀 (Excellent)"
        elif score >= 70:
            return "良好 (Good)"
        elif score >= 60:
            return "及格 (Pass)"
        elif score >= 50:
            return "需要改进 (Needs Improvement)"
        else:
            return "需要大量练习 (Needs Much Practice)"
    
    def get_suggestions(self, overall_score: float, scores: List[float]) -> List[str]:
        """
        根据分数提供改进建议
        
        Args:
            overall_score: 总体分数
            scores: 各段分数列表
            
        Returns:
            建议列表
        """
        suggestions = []
        
        if overall_score < 60:
            suggestions.append("整体动作需要大幅改进，建议多观看标准视频并反复练习")
        elif overall_score < 80:
            suggestions.append("动作基本正确，但细节需要完善")
        
        # 分析分数波动
        score_std = np.std(scores)
        if score_std > 15:
            suggestions.append("动作一致性需要提高，某些片段表现差异较大")
        
        # 找出表现最差的片段
        min_score_idx = scores.index(min(scores))
        if min(scores) < overall_score - 20:
            suggestions.append(f"第{min_score_idx + 1}段动作需要重点练习")
        
        return suggestions

def main():
    """
    主函数，演示如何使用舞蹈相似度计算器
    """
    # 创建计算器实例
    calculator = DanceSimilarityCalculator()
    
    # 计算相似度
    result = calculator.calculate_overall_similarity(window_size=10, step_size=5)
    
    # 输出结果
    print("=" * 50)
    print("舞蹈动作相似度分析报告")
    print("=" * 50)
    print(f"总体得分: {result['overall_score']}/100")
    print(f"表现等级: {result['performance_level']}")
    print(f"最高分段: {result['max_score']}/100")
    print(f"最低分段: {result['min_score']}/100")
    print(f"分数标准差: {result['score_std']}")
    print(f"分析帧数: {result['total_frames_analyzed']}")
    
    print("\n改进建议:")
    for i, suggestion in enumerate(result['suggestions'], 1):
        print(f"{i}. {suggestion}")
    
    print("\n详细分段分析:")
    for detail in result['frame_details'][:5]:  # 只显示前5段
        print(f"用户帧 {detail['user_start_frame']}-{detail['user_end_frame']} "
              f"vs 视频帧 {detail['video_match_start']}-{detail['video_match_end']}: "
              f"{detail['similarity_score']}/100")

if __name__ == "__main__":
    main()
