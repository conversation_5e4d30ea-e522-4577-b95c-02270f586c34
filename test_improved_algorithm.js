#!/usr/bin/env node

/**
 * 测试改进后的舞蹈相似度算法
 */

const fs = require('fs').promises;
const DanceSimilarityCalculator = require('./danceSimilarity.js');

async function testImprovedAlgorithm() {
    console.log('🧪 测试改进后的算法...');
    
    try {
        // 加载少量数据进行测试
        const videoData = JSON.parse(await fs.readFile('videoPoint.json', 'utf8'));
        const userData = JSON.parse(await fs.readFile('userPoint.json', 'utf8'));
        
        console.log(`📊 数据加载完成: 视频${videoData.length}帧, 用户${userData.length}帧`);
        
        const calculator = new DanceSimilarityCalculator();
        
        // 测试单帧计算
        console.log('\n🔍 测试单帧相似度计算...');
        const startTime = Date.now();
        
        const testFrames = Math.min(10, videoData.length, userData.length);
        const similarities = [];
        
        for (let i = 0; i < testFrames; i++) {
            const similarity = calculator.calculateFrameSimilarity(videoData[i], userData[i]);
            similarities.push(similarity);
            console.log(`  帧 ${i + 1}: ${similarity.toFixed(2)}/100`);
        }
        
        const avgSimilarity = similarities.reduce((a, b) => a + b, 0) / similarities.length;
        const duration = Date.now() - startTime;
        
        console.log(`\n📈 前${testFrames}帧平均相似度: ${avgSimilarity.toFixed(2)}/100`);
        console.log(`⏱️  计算耗时: ${duration}ms (${(duration/testFrames).toFixed(1)}ms/帧)`);
        
        // 测试归一化功能
        console.log('\n🔧 测试姿态归一化...');
        const normalizedVideo = calculator.normalizePose(videoData[0]);
        const normalizedUser = calculator.normalizePose(userData[0]);
        
        console.log('  原始视频第一个点:', videoData[0][0]);
        console.log('  归一化后:', normalizedVideo[0]);
        console.log('  原始用户第一个点:', userData[0][0]);
        console.log('  归一化后:', normalizedUser[0]);
        
        // 测试小数据集的完整分析
        console.log('\n🎯 测试小数据集完整分析...');
        const smallVideoData = videoData.slice(0, 50);
        const smallUserData = userData.slice(0, 50);
        
        const result = await calculator.quickScore(smallVideoData, smallUserData);
        
        if (result.error) {
            console.log(`❌ 分析失败: ${result.error}`);
        } else {
            console.log(`📊 小数据集得分: ${result.score}/100 (${result.grade})`);
            console.log(`🎯 最佳匹配: 视频第${result.bestMatchInfo.videoStart}帧 vs 用户第${result.bestMatchInfo.userStart}帧`);
        }
        
    } catch (error) {
        console.error(`❌ 测试失败: ${error.message}`);
        console.error(error.stack);
    }
}

// 创建测试用的相同姿态数据
function createTestData() {
    console.log('\n🧪 创建测试数据...');
    
    // 创建一个标准姿态
    const standardPose = [
        {x: 0.5, y: 0.3},   // 0: 鼻子
        {x: 0.48, y: 0.28}, // 1: 左眼
        {x: 0.52, y: 0.28}, // 2: 右眼
        {x: 0.46, y: 0.26}, // 3: 左耳
        {x: 0.54, y: 0.26}, // 4: 右耳
        {x: 0.4, y: 0.4},   // 5: 左肩
        {x: 0.6, y: 0.4},   // 6: 右肩
        {x: 0.35, y: 0.5},  // 7: 左肘
        {x: 0.65, y: 0.5},  // 8: 右肘
        {x: 0.3, y: 0.6},   // 9: 左腕
        {x: 0.7, y: 0.6},   // 10: 右腕
        {x: 0.45, y: 0.7},  // 11: 左髋
        {x: 0.55, y: 0.7},  // 12: 右髋
        {x: 0.43, y: 0.85}, // 13: 左膝
        {x: 0.57, y: 0.85}, // 14: 右膝
        {x: 0.41, y: 0.95}, // 15: 左踝
        {x: 0.59, y: 0.95}  // 16: 右踝
    ];
    
    const calculator = new DanceSimilarityCalculator();
    
    // 测试完全相同的姿态
    console.log('  测试相同姿态:');
    const sameSimilarity = calculator.calculateFrameSimilarity(standardPose, standardPose);
    console.log(`    相似度: ${sameSimilarity.toFixed(2)}/100 (应该接近100)`);
    
    // 测试稍有差异的姿态
    const slightlyDifferent = standardPose.map(point => ({
        x: point.x + (Math.random() - 0.5) * 0.02, // ±1%的随机偏移
        y: point.y + (Math.random() - 0.5) * 0.02
    }));
    
    console.log('  测试轻微差异姿态:');
    const slightSimilarity = calculator.calculateFrameSimilarity(standardPose, slightlyDifferent);
    console.log(`    相似度: ${slightSimilarity.toFixed(2)}/100 (应该在90+)`);
    
    // 测试差异较大的姿态
    const veryDifferent = standardPose.map(point => ({
        x: point.x + (Math.random() - 0.5) * 0.2, // ±10%的随机偏移
        y: point.y + (Math.random() - 0.5) * 0.2
    }));
    
    console.log('  测试差异较大姿态:');
    const differentSimilarity = calculator.calculateFrameSimilarity(standardPose, veryDifferent);
    console.log(`    相似度: ${differentSimilarity.toFixed(2)}/100 (应该较低)`);
    
    // 测试完全不同的姿态
    const totallyDifferent = standardPose.map(() => ({
        x: Math.random(),
        y: Math.random()
    }));
    
    console.log('  测试完全不同姿态:');
    const totallyDifferentSimilarity = calculator.calculateFrameSimilarity(standardPose, totallyDifferent);
    console.log(`    相似度: ${totallyDifferentSimilarity.toFixed(2)}/100 (应该很低)`);
}

async function main() {
    console.log('🚀 改进算法测试程序');
    console.log('='.repeat(50));
    
    // 先测试基本功能
    createTestData();
    
    // 再测试真实数据
    await testImprovedAlgorithm();
    
    console.log('\n✅ 测试完成!');
}

if (require.main === module) {
    main().catch(error => {
        console.error(`❌ 程序执行失败: ${error.message}`);
        process.exit(1);
    });
}
