/**
 * 舞蹈动作相似度计算器 - JavaScript版本
 * 支持关键点坐标数据的相似度分析
 */

class DanceSimilarityCalculator {
    constructor() {
        // 关键点权重 - 根据身体部位重要性设置
        this.pointWeights = [
            1.0,  // 鼻子
            1.2,  // 左眼
            1.2,  // 右眼  
            1.0,  // 左耳
            1.0,  // 右耳
            1.5,  // 左肩
            1.5,  // 右肩
            1.3,  // 左肘
            1.3,  // 右肘
            1.0,  // 左腕
            1.0,  // 右腕
            2.0,  // 左髋
            2.0,  // 右髋
            1.8,  // 左膝
            1.8,  // 右膝
            1.5,  // 左踝
            1.5   // 右踝
        ];
    }

    /**
     * 计算两点之间的欧几里得距离
     */
    calculateDistance(point1, point2) {
        if (!point1 || !point2) return 1.0; // 如果点不存在，返回最大距离
        
        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算单帧的相似度分数
     */
    calculateFrameSimilarity(videoFrame, userFrame) {
        if (!videoFrame || !userFrame) return 0;
        if (videoFrame.length !== userFrame.length) return 0;

        let totalWeightedScore = 0;
        let totalWeight = 0;
        const maxDistance = Math.sqrt(2); // 最大可能距离 (对角线)

        for (let i = 0; i < Math.min(videoFrame.length, this.pointWeights.length); i++) {
            const distance = this.calculateDistance(videoFrame[i], userFrame[i]);
            
            // 将距离转换为相似度分数 (0-100)
            const similarity = Math.max(0, 100 * (1 - distance / maxDistance));
            
            const weight = this.pointWeights[i] || 1.0;
            totalWeightedScore += similarity * weight;
            totalWeight += weight;
        }

        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    }

    /**
     * 找到最佳匹配段落
     */
    findBestMatchingSegment(videoData, userData, segmentLength = 10) {
        if (!videoData || !userData || videoData.length === 0 || userData.length === 0) {
            return { score: 0, videoStart: 0, userStart: 0 };
        }

        let bestScore = 0;
        let bestVideoStart = 0;
        let bestUserStart = 0;

        // 采样策略 - 如果数据太大，进行采样
        const maxFrames = 200;
        const videoStep = Math.max(1, Math.floor(videoData.length / maxFrames));
        const userStep = Math.max(1, Math.floor(userData.length / maxFrames));

        for (let userStart = 0; userStart < userData.length - segmentLength; userStart += userStep) {
            for (let videoStart = 0; videoStart < videoData.length - segmentLength; videoStart += videoStep) {
                let segmentScore = 0;
                let validFrames = 0;

                for (let i = 0; i < segmentLength; i++) {
                    if (userStart + i < userData.length && videoStart + i < videoData.length) {
                        const frameScore = this.calculateFrameSimilarity(
                            videoData[videoStart + i],
                            userData[userStart + i]
                        );
                        segmentScore += frameScore;
                        validFrames++;
                    }
                }

                if (validFrames > 0) {
                    const avgScore = segmentScore / validFrames;
                    if (avgScore > bestScore) {
                        bestScore = avgScore;
                        bestVideoStart = videoStart;
                        bestUserStart = userStart;
                    }
                }
            }
        }

        return {
            score: Math.round(bestScore * 100) / 100,
            videoStart: bestVideoStart,
            userStart: bestUserStart,
            segmentLength: segmentLength
        };
    }

    /**
     * 快速计算舞蹈得分
     */
    async quickScore(videoData, userData) {
        if (!videoData || !userData) {
            return { error: "数据无效", score: 0 };
        }

        console.log(`📊 数据信息: 视频${videoData.length}帧, 用户${userData.length}帧`);

        // 计算不同长度段落的得分
        const segmentLengths = [5, 10, 15];
        const results = [];

        for (const length of segmentLengths) {
            if (length <= Math.min(videoData.length, userData.length)) {
                console.log(`🔄 计算${length}帧段落相似度...`);
                const result = this.findBestMatchingSegment(videoData, userData, length);
                result.segmentType = `${length}帧`;
                results.push(result);
            }
        }

        if (results.length === 0) {
            return { error: "无法计算相似度", score: 0 };
        }

        // 使用10帧段落作为主要得分
        const mainResult = results.find(r => r.segmentType === "10帧") || results[results.length - 1];
        const overallScore = mainResult.score;

        // 生成评级
        let grade, level;
        if (overallScore >= 90) {
            grade = "A+"; level = "完美";
        } else if (overallScore >= 85) {
            grade = "A"; level = "优秀";
        } else if (overallScore >= 80) {
            grade = "B+"; level = "良好";
        } else if (overallScore >= 75) {
            grade = "B"; level = "中等偏上";
        } else if (overallScore >= 70) {
            grade = "C+"; level = "中等";
        } else if (overallScore >= 60) {
            grade = "C"; level = "及格";
        } else {
            grade = "D"; level = "需要努力";
        }

        return {
            score: overallScore,
            grade: grade,
            level: level,
            segmentResults: results,
            videoFrames: videoData.length,
            userFrames: userData.length,
            bestMatchInfo: {
                videoStart: mainResult.videoStart,
                userStart: mainResult.userStart,
                segmentLength: mainResult.segmentLength
            }
        };
    }

    /**
     * 分析特定段落的详细相似度
     */
    analyzeSpecificSegment(videoData, userData, videoStart, userStart, length = 10) {
        if (videoStart + length > videoData.length || userStart + length > userData.length) {
            return { error: "段落超出数据范围" };
        }

        const frameScores = [];
        
        for (let i = 0; i < length; i++) {
            const frameScore = this.calculateFrameSimilarity(
                videoData[videoStart + i],
                userData[userStart + i]
            );
            
            frameScores.push({
                frame: i,
                score: Math.round(frameScore * 100) / 100
            });
        }

        const overallScore = frameScores.reduce((sum, frame) => sum + frame.score, 0) / frameScores.length;

        return {
            overallScore: Math.round(overallScore * 100) / 100,
            frameDetails: frameScores,
            segmentInfo: {
                videoStart: videoStart,
                userStart: userStart,
                length: length
            }
        };
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DanceSimilarityCalculator;
}

// 全局可用
if (typeof window !== 'undefined') {
    window.DanceSimilarityCalculator = DanceSimilarityCalculator;
}
