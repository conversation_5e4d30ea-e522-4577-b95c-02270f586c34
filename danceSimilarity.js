/**
 * 舞蹈动作相似度计算器 - JavaScript版本
 * 支持关键点坐标数据的相似度分析
 */

class DanceSimilarityCalculator {
    constructor() {
        // 关键点权重 - 舞蹈动作中各部位的重要性
        this.pointWeights = [
            0.5,  // 0: 鼻子 - 头部稳定性
            0.3,  // 1: 左眼 - 面部朝向
            0.3,  // 2: 右眼 - 面部朝向
            0.2,  // 3: 左耳 - 次要
            0.2,  // 4: 右耳 - 次要
            3.0,  // 5: 左肩 - 核心动作
            3.0,  // 6: 右肩 - 核心动作
            2.5,  // 7: 左肘 - 手臂动作
            2.5,  // 8: 右肘 - 手臂动作
            2.0,  // 9: 左腕 - 手部表现
            2.0,  // 10: 右腕 - 手部表现
            4.0,  // 11: 左髋 - 最重要
            4.0,  // 12: 右髋 - 最重要
            3.5,  // 13: 左膝 - 腿部动作
            3.5,  // 14: 右膝 - 腿部动作
            3.0,  // 15: 左踝 - 脚部动作
            3.0   // 16: 右踝 - 脚部动作
        ];

        // 关键连接线 - 用于计算角度和比例
        this.connections = [
            [5, 6],   // 肩膀连线
            [11, 12], // 髋部连线
            [5, 11],  // 左侧躯干
            [6, 12],  // 右侧躯干
            [5, 7],   // 左上臂
            [6, 8],   // 右上臂
            [7, 9],   // 左前臂
            [8, 10],  // 右前臂
            [11, 13], // 左大腿
            [12, 14], // 右大腿
            [13, 15], // 左小腿
            [14, 16]  // 右小腿
        ];
    }

    /**
     * 归一化姿态 - 基于肩膀和髋部的距离进行缩放和平移
     */
    normalizePose(points) {
        if (!points || points.length < 17) return points;

        // 计算肩膀中心和髋部中心
        const shoulderCenter = {
            x: (points[5].x + points[6].x) / 2,
            y: (points[5].y + points[6].y) / 2
        };

        const hipCenter = {
            x: (points[11].x + points[12].x) / 2,
            y: (points[11].y + points[12].y) / 2
        };

        // 计算躯干长度作为缩放基准
        const torsoLength = Math.sqrt(
            Math.pow(shoulderCenter.x - hipCenter.x, 2) +
            Math.pow(shoulderCenter.y - hipCenter.y, 2)
        );

        if (torsoLength === 0) return points;

        // 以髋部中心为原点进行归一化
        return points.map(point => {
            if (!point) return point;
            return {
                x: (point.x - hipCenter.x) / torsoLength,
                y: (point.y - hipCenter.y) / torsoLength
            };
        });
    }

    /**
     * 计算两个归一化点之间的距离
     */
    calculateNormalizedDistance(point1, point2) {
        if (!point1 || !point2) return 2.0; // 更严格的惩罚

        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算角度相似度 - 基于关键连接线的角度
     */
    calculateAngleSimilarity(videoPoints, userPoints) {
        let totalSimilarity = 0;
        let validConnections = 0;

        for (const [p1, p2] of this.connections) {
            if (videoPoints[p1] && videoPoints[p2] && userPoints[p1] && userPoints[p2]) {
                // 计算角度
                const videoAngle = Math.atan2(
                    videoPoints[p2].y - videoPoints[p1].y,
                    videoPoints[p2].x - videoPoints[p1].x
                );

                const userAngle = Math.atan2(
                    userPoints[p2].y - userPoints[p1].y,
                    userPoints[p2].x - userPoints[p1].x
                );

                // 计算角度差值 (0-π)
                let angleDiff = Math.abs(videoAngle - userAngle);
                if (angleDiff > Math.PI) angleDiff = 2 * Math.PI - angleDiff;

                // 转换为相似度 (角度差越小相似度越高)
                const similarity = Math.max(0, 100 * (1 - angleDiff / Math.PI));
                totalSimilarity += similarity;
                validConnections++;
            }
        }

        return validConnections > 0 ? totalSimilarity / validConnections : 0;
    }

    /**
     * 计算单帧的相似度分数 - 改进版本
     */
    calculateFrameSimilarity(videoFrame, userFrame) {
        if (!videoFrame || !userFrame) return 0;
        if (videoFrame.length !== userFrame.length) return 0;

        // 归一化姿态
        const normalizedVideo = this.normalizePose(videoFrame);
        const normalizedUser = this.normalizePose(userFrame);

        // 计算位置相似度 (权重40%)
        let positionScore = 0;
        let totalWeight = 0;

        for (let i = 0; i < Math.min(normalizedVideo.length, this.pointWeights.length); i++) {
            if (normalizedVideo[i] && normalizedUser[i]) {
                const distance = this.calculateNormalizedDistance(normalizedVideo[i], normalizedUser[i]);

                // 更严格的相似度计算 - 距离阈值为0.3 (相对于躯干长度)
                const maxAllowedDistance = 0.3;
                const similarity = Math.max(0, 100 * (1 - Math.min(distance, maxAllowedDistance) / maxAllowedDistance));

                const weight = this.pointWeights[i];
                positionScore += similarity * weight;
                totalWeight += weight;
            }
        }

        positionScore = totalWeight > 0 ? positionScore / totalWeight : 0;

        // 计算角度相似度 (权重60%)
        const angleScore = this.calculateAngleSimilarity(normalizedVideo, normalizedUser);

        // 综合得分
        const finalScore = positionScore * 0.4 + angleScore * 0.6;

        return Math.max(0, Math.min(100, finalScore));
    }

    /**
     * 找到最佳匹配段落
     */
    findBestMatchingSegment(videoData, userData, segmentLength = 10) {
        if (!videoData || !userData || videoData.length === 0 || userData.length === 0) {
            return { score: 0, videoStart: 0, userStart: 0 };
        }

        let bestScore = 0;
        let bestVideoStart = 0;
        let bestUserStart = 0;

        // 采样策略 - 如果数据太大，进行采样
        const maxFrames = 200;
        const videoStep = Math.max(1, Math.floor(videoData.length / maxFrames));
        const userStep = Math.max(1, Math.floor(userData.length / maxFrames));

        for (let userStart = 0; userStart < userData.length - segmentLength; userStart += userStep) {
            for (let videoStart = 0; videoStart < videoData.length - segmentLength; videoStart += videoStep) {
                let segmentScore = 0;
                let validFrames = 0;

                for (let i = 0; i < segmentLength; i++) {
                    if (userStart + i < userData.length && videoStart + i < videoData.length) {
                        const frameScore = this.calculateFrameSimilarity(
                            videoData[videoStart + i],
                            userData[userStart + i]
                        );
                        segmentScore += frameScore;
                        validFrames++;
                    }
                }

                if (validFrames > 0) {
                    const avgScore = segmentScore / validFrames;
                    if (avgScore > bestScore) {
                        bestScore = avgScore;
                        bestVideoStart = videoStart;
                        bestUserStart = userStart;
                    }
                }
            }
        }

        return {
            score: Math.round(bestScore * 100) / 100,
            videoStart: bestVideoStart,
            userStart: bestUserStart,
            segmentLength: segmentLength
        };
    }

    /**
     * 快速计算舞蹈得分
     */
    async quickScore(videoData, userData) {
        if (!videoData || !userData) {
            return { error: "数据无效", score: 0 };
        }

        console.log(`📊 数据信息: 视频${videoData.length}帧, 用户${userData.length}帧`);

        // 计算不同长度段落的得分
        const segmentLengths = [5, 10, 15];
        const results = [];

        for (const length of segmentLengths) {
            if (length <= Math.min(videoData.length, userData.length)) {
                console.log(`🔄 计算${length}帧段落相似度...`);
                const result = this.findBestMatchingSegment(videoData, userData, length);
                result.segmentType = `${length}帧`;
                results.push(result);
            }
        }

        if (results.length === 0) {
            return { error: "无法计算相似度", score: 0 };
        }

        // 使用10帧段落作为主要得分
        const mainResult = results.find(r => r.segmentType === "10帧") || results[results.length - 1];
        const overallScore = mainResult.score;

        // 生成评级
        let grade, level;
        if (overallScore >= 90) {
            grade = "A+"; level = "完美";
        } else if (overallScore >= 85) {
            grade = "A"; level = "优秀";
        } else if (overallScore >= 80) {
            grade = "B+"; level = "良好";
        } else if (overallScore >= 75) {
            grade = "B"; level = "中等偏上";
        } else if (overallScore >= 70) {
            grade = "C+"; level = "中等";
        } else if (overallScore >= 60) {
            grade = "C"; level = "及格";
        } else {
            grade = "D"; level = "需要努力";
        }

        return {
            score: overallScore,
            grade: grade,
            level: level,
            segmentResults: results,
            videoFrames: videoData.length,
            userFrames: userData.length,
            bestMatchInfo: {
                videoStart: mainResult.videoStart,
                userStart: mainResult.userStart,
                segmentLength: mainResult.segmentLength
            }
        };
    }

    /**
     * 分析特定段落的详细相似度
     */
    analyzeSpecificSegment(videoData, userData, videoStart, userStart, length = 10) {
        if (videoStart + length > videoData.length || userStart + length > userData.length) {
            return { error: "段落超出数据范围" };
        }

        const frameScores = [];
        
        for (let i = 0; i < length; i++) {
            const frameScore = this.calculateFrameSimilarity(
                videoData[videoStart + i],
                userData[userStart + i]
            );
            
            frameScores.push({
                frame: i,
                score: Math.round(frameScore * 100) / 100
            });
        }

        const overallScore = frameScores.reduce((sum, frame) => sum + frame.score, 0) / frameScores.length;

        return {
            overallScore: Math.round(overallScore * 100) / 100,
            frameDetails: frameScores,
            segmentInfo: {
                videoStart: videoStart,
                userStart: userStart,
                length: length
            }
        };
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DanceSimilarityCalculator;
}

// 全局可用
if (typeof window !== 'undefined') {
    window.DanceSimilarityCalculator = DanceSimilarityCalculator;
}
