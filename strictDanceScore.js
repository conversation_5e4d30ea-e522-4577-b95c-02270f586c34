#!/usr/bin/env node

/**
 * 严格版本的舞蹈动作相似度计算 - Node.js命令行工具
 * 使用更准确的评分算法，避免虚高的分数
 */

const fs = require('fs').promises;
const StrictDanceSimilarityCalculator = require('./strictDanceSimilarity.js');

async function loadData(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        throw new Error(`无法加载文件 ${filePath}: ${error.message}`);
    }
}

function printStrictReport(result) {
    if (result.error) {
        console.log(`❌ 错误: ${result.error}`);
        return;
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎭 严格舞蹈相似度分析报告');
    console.log('='.repeat(50));
    console.log(`📊 总体得分: ${result.score}/100`);
    console.log(`🏆 评级: ${result.grade} (${result.level})`);
    console.log(`📋 数据: 视频${result.videoFrames}帧, 用户${result.userFrames}帧`);
    
    console.log('\n📈 分段分析:');
    result.segmentResults.forEach(seg => {
        console.log(`   ${seg.segmentType}: ${seg.score}/100`);
    });
    
    const best = result.bestMatchInfo;
    console.log(`\n🎯 最佳匹配: 视频第${best.videoStart}帧 vs 用户第${best.userStart}帧 (${best.segmentLength}帧段落)`);
    
    // 添加评分说明
    console.log('\n📝 评分说明:');
    console.log('   本工具使用严格的评分标准:');
    console.log('   • 角度差异超过30度将大幅扣分');
    console.log('   • 位置偏移超过15%躯干长度将大幅扣分');
    console.log('   • 重要关节(肩、髋、膝)权重更高');
    console.log('   • 评分更能反映真实的动作差异');
    
    // 添加改进建议
    if (result.score < 40) {
        console.log('\n💡 改进建议:');
        console.log('   • 动作差异较大，建议重新学习基本动作');
        console.log('   • 重点关注肩部、髋部、膝部的位置');
        console.log('   • 多观看标准视频，注意动作细节');
    } else if (result.score < 60) {
        console.log('\n💡 改进建议:');
        console.log('   • 基本动作框架正确，需要精细调整');
        console.log('   • 注意手臂和腿部的角度协调');
        console.log('   • 加强动作的一致性练习');
    } else if (result.score < 80) {
        console.log('\n💡 改进建议:');
        console.log('   • 动作已经比较标准，继续保持');
        console.log('   • 可以尝试更复杂的舞蹈动作');
        console.log('   • 注意动作的流畅性和表现力');
    } else {
        console.log('\n🎉 恭喜! 你的动作非常标准!');
    }
}

async function main() {
    const args = process.argv.slice(2);
    
    // 检查选项
    const isQuick = args.includes('--quick');
    const showComparison = args.includes('--compare');
    
    // 过滤掉选项，只保留文件路径参数
    const fileArgs = args.filter(arg => !arg.startsWith('--'));
    
    // 默认文件路径
    const videoFile = fileArgs[0] || 'videoPoint.json';
    const userFile = fileArgs[1] || 'userPoint.json';

    try {
        console.log('🚀 开始严格舞蹈相似度分析...');
        
        // 加载数据
        const [videoData, userData] = await Promise.all([
            loadData(videoFile),
            loadData(userFile)
        ]);

        // 创建严格计算器并分析
        const calculator = new StrictDanceSimilarityCalculator();
        const result = await calculator.strictQuickScore(videoData, userData);

        if (isQuick) {
            console.log(`得分: ${result.score}/100 (${result.grade}) - 严格评分`);
        } else {
            printStrictReport(result);
        }

        // 如果需要对比
        if (showComparison) {
            console.log('\n🔍 与原始算法对比:');
            const OriginalCalculator = require('./danceSimilarity.js');
            const originalCalc = new OriginalCalculator();
            const originalResult = await originalCalc.quickScore(videoData, userData);
            
            console.log(`原始算法: ${originalResult.score}/100 (${originalResult.grade})`);
            console.log(`严格算法: ${result.score}/100 (${result.grade})`);
            console.log(`差异: ${(originalResult.score - result.score).toFixed(1)}分`);
        }

        process.exit(0);

    } catch (error) {
        console.error(`❌ 发生错误: ${error.message}`);
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
严格舞蹈动作相似度计算工具 - JavaScript版本

使用方法:
  node strictDanceScore.js [videoFile] [userFile] [options]

参数:
  videoFile    标准视频数据文件路径 (默认: videoPoint.json)
  userFile     用户动作数据文件路径 (默认: userPoint.json)

选项:
  --quick      只显示得分，不显示详细报告
  --compare    同时显示与原始算法的对比
  --help       显示此帮助信息

特点:
  • 使用严格的评分标准，避免虚高分数
  • 角度差异超过30度大幅扣分
  • 位置偏移超过15%躯干长度大幅扣分
  • 重要关节权重更高
  • 更能反映真实的动作差异

示例:
  node strictDanceScore.js                                    # 使用默认文件
  node strictDanceScore.js videoPoint.json userPoint.json    # 指定文件
  node strictDanceScore.js --quick                            # 快速得分
  node strictDanceScore.js --compare                          # 对比两种算法
    `);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 运行主程序
if (require.main === module) {
    main().catch(error => {
        console.error(`❌ 未处理的错误: ${error.message}`);
        process.exit(1);
    });
}

module.exports = StrictDanceSimilarityCalculator;
