import json
import math
from typing import List, Dict

def load_dance_data(file_path: str) -> List[List[float]]:
    """加载舞蹈数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {e}")
        return []

def angle_difference(angle1: float, angle2: float) -> float:
    """计算两个角度的最小差值 (0-180度)"""
    angle1 = angle1 % 360
    angle2 = angle2 % 360
    diff = abs(angle1 - angle2)
    return min(diff, 360 - diff)

def calculate_frame_score(video_frame: List[float], user_frame: List[float]) -> float:
    """
    计算单帧动作得分 (0-100分)
    
    Args:
        video_frame: 标准视频的8个角度 [左臂, 左前臂, 右臂, 右前臂, 左腿, 左小腿, 右腿, 右小腿]
        user_frame: 用户动作的8个角度
    
    Returns:
        得分 (0-100)
    """
    if len(video_frame) != 8 or len(user_frame) != 8:
        return 0.0
    
    # 各部位权重 (腿部动作更重要)
    weights = [1.0, 0.8, 1.0, 0.8, 1.5, 1.2, 1.5, 1.2]
    
    total_score = 0.0
    total_weight = sum(weights)
    
    for i in range(8):
        # 计算角度差值
        diff = angle_difference(video_frame[i], user_frame[i])
        
        # 转换为得分 (差值越小得分越高)
        angle_score = max(0, 100 - (diff / 180) * 100)
        
        # 应用权重
        total_score += angle_score * weights[i]
    
    return total_score / total_weight

def find_best_matching_segment(video_data: List[List[float]], 
                              user_data: List[List[float]], 
                              segment_length: int = 10) -> Dict:
    """
    找到用户动作在视频中的最佳匹配段落
    
    Args:
        video_data: 标准视频数据
        user_data: 用户动作数据
        segment_length: 匹配段落长度
    
    Returns:
        匹配结果字典
    """
    best_score = 0.0
    best_video_start = 0
    best_user_start = 0
    
    # 遍历用户数据的每个可能起始位置
    for user_start in range(len(user_data) - segment_length + 1):
        # 在视频数据中寻找最佳匹配
        for video_start in range(len(video_data) - segment_length + 1):
            segment_scores = []
            
            # 计算这个段落的平均得分
            for i in range(segment_length):
                if (user_start + i < len(user_data) and 
                    video_start + i < len(video_data)):
                    score = calculate_frame_score(
                        video_data[video_start + i],
                        user_data[user_start + i]
                    )
                    segment_scores.append(score)
            
            if segment_scores:
                avg_score = sum(segment_scores) / len(segment_scores)
                if avg_score > best_score:
                    best_score = avg_score
                    best_video_start = video_start
                    best_user_start = user_start
    
    return {
        'score': round(best_score, 2),
        'video_start_frame': best_video_start,
        'user_start_frame': best_user_start,
        'segment_length': segment_length
    }

def analyze_dance_performance(video_file: str = "video.json", 
                            user_file: str = "user.json") -> Dict:
    """
    分析舞蹈表现并给出详细报告
    
    Args:
        video_file: 标准视频文件路径
        user_file: 用户动作文件路径
    
    Returns:
        分析报告字典
    """
    # 加载数据
    video_data = load_dance_data(video_file)
    user_data = load_dance_data(user_file)
    
    if not video_data or not user_data:
        return {"error": "数据加载失败"}
    
    # 分析不同长度的段落
    segment_results = []
    for length in [5, 10, 15, 20]:
        if length <= min(len(video_data), len(user_data)):
            result = find_best_matching_segment(video_data, user_data, length)
            result['segment_type'] = f"{length}帧段落"
            segment_results.append(result)
    
    # 计算总体得分 (使用10帧段落作为主要评分)
    main_result = find_best_matching_segment(video_data, user_data, 10)
    overall_score = main_result['score']
    
    # 生成评级和建议
    if overall_score >= 90:
        grade = "A+"
        level = "完美"
        suggestions = ["动作非常标准，继续保持！"]
    elif overall_score >= 85:
        grade = "A"
        level = "优秀"
        suggestions = ["动作很好，可以尝试更复杂的舞蹈"]
    elif overall_score >= 80:
        grade = "B+"
        level = "良好"
        suggestions = ["动作基本正确，注意细节的完善"]
    elif overall_score >= 75:
        grade = "B"
        level = "中等偏上"
        suggestions = ["继续练习，注意动作的准确性"]
    elif overall_score >= 70:
        grade = "C+"
        level = "中等"
        suggestions = ["需要更多练习，重点关注基本动作"]
    elif overall_score >= 60:
        grade = "C"
        level = "及格"
        suggestions = ["动作需要改进，建议分解练习各个部位"]
    else:
        grade = "D"
        level = "需要努力"
        suggestions = ["建议从基础动作开始练习", "多观看标准视频进行对比"]
    
    return {
        "overall_score": overall_score,
        "grade": grade,
        "performance_level": level,
        "suggestions": suggestions,
        "segment_analysis": segment_results,
        "data_info": {
            "video_frames": len(video_data),
            "user_frames": len(user_data),
            "best_match_video_start": main_result['video_start_frame'],
            "best_match_user_start": main_result['user_start_frame']
        }
    }

def print_analysis_report(analysis: Dict):
    """打印分析报告"""
    if "error" in analysis:
        print(f"错误: {analysis['error']}")
        return
    
    print("=" * 60)
    print("🎭 舞蹈动作相似度分析报告 🎭")
    print("=" * 60)
    
    print(f"📊 总体得分: {analysis['overall_score']}/100")
    print(f"🏆 评级: {analysis['grade']}")
    print(f"📈 表现水平: {analysis['performance_level']}")
    
    print(f"\n📋 数据信息:")
    print(f"   标准视频帧数: {analysis['data_info']['video_frames']}")
    print(f"   用户动作帧数: {analysis['data_info']['user_frames']}")
    print(f"   最佳匹配位置: 视频第{analysis['data_info']['best_match_video_start']}帧 "
          f"vs 用户第{analysis['data_info']['best_match_user_start']}帧")
    
    print(f"\n📝 改进建议:")
    for i, suggestion in enumerate(analysis['suggestions'], 1):
        print(f"   {i}. {suggestion}")
    
    print(f"\n📊 分段分析:")
    for segment in analysis['segment_analysis']:
        print(f"   {segment['segment_type']}: {segment['score']}/100 "
              f"(视频帧{segment['video_start_frame']}-{segment['video_start_frame']+segment['segment_length']-1} "
              f"vs 用户帧{segment['user_start_frame']}-{segment['user_start_frame']+segment['segment_length']-1})")

def quick_score(video_file: str = "video.json", user_file: str = "user.json") -> float:
    """
    快速获取舞蹈得分
    
    Returns:
        得分 (0-100)
    """
    analysis = analyze_dance_performance(video_file, user_file)
    return analysis.get('overall_score', 0.0)

# 使用示例
if __name__ == "__main__":
    # 分析舞蹈表现
    result = analyze_dance_performance()
    
    # 打印详细报告
    print_analysis_report(result)
    
    print("\n" + "=" * 60)
    print("🎯 快速得分测试")
    print("=" * 60)
    score = quick_score()
    print(f"你的舞蹈得分: {score}/100")
