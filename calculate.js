import userData from './userData.js';
import videoData from './videoData.js';

// validate.js
function validateSequence(sequence, sequenceParameterName) {
    if (!(Array.isArray(sequence))) {
        throw new TypeError('Invalid sequence \'' + sequenceParameterName + '\' type: expected an array');
    }

    if (sequence.length < 1) {
        throw new Error('Invalid number of sequence data points for \'' + sequenceParameterName + '\': expected at least one');
    }

    if (typeof sequence[0] !== 'number') {
        throw new TypeError('Invalid data points types for sequence \'' + sequenceParameterName + '\': expected a number');
    }
}


// matrix.js
var createArray = function (length, value) {
    if (typeof length !== 'number') {
        throw new TypeError('Invalid length type');
    }

    if (typeof value === 'undefined') {
        throw new Error('Invalid value: expected a value to be provided');
    }

    var array = new Array(length);
    for (var index = 0; index < length; index++) {
        array[index] = value;
    }

    return array;
};

var createMatrix = function (m, n, value) {
    var matrix = [];
    for (var rowIndex = 0; rowIndex < m; rowIndex++) {
        matrix.push(createArray(n, value));
    }

    return matrix;
};



// comparison.js
var EPSILON = 2.2204460492503130808472633361816E-16;

var nearlyEqual = function (i, j, epsilon) {
    var iAbsolute= Math.abs(i);
    var jAbsolute = Math.abs(j);
    var difference = Math.abs(i - j);
    var equal = i === j;
    if (!equal) {
        equal = difference < EPSILON;
        if (!equal) {
            equal = difference <= Math.max(iAbsolute, jAbsolute) * epsilon;
        }
    }

    return equal;
};


// manhattan.js
var manhattan = {
    distance: function (x, y) {
        var difference = x - y;
        var manhattanDistance = Math.abs(difference);
        return manhattanDistance;
    }
}


// euclidean.js
var euclidean = {
    distance: function (x, y) {
        var difference = x - y;
        // 减少距离，小于10的认为是一样的动作，大于10的，缩小差距
        var absDifference = Math.abs(difference);
        var threshold = 3;
        difference = absDifference < threshold ? 0 : (absDifference - threshold);
        var euclideanDistance = Math.sqrt(difference * difference);
        return euclideanDistance;
    }
}

// squaredEuclidean.js
var squaredEuclidean = {
    distance: function (x, y) {
        var difference = x - y;
        var squaredEuclideanDistance = difference * difference;
        return squaredEuclideanDistance;
    }
}

function validateOptions(options) {
    if (typeof options !== 'object') {
        throw new TypeError('Invalid options type: expected an object');
    } else if (typeof options.distanceMetric !== 'string' && typeof options.distanceFunction !== 'function') {
        throw new TypeError('Invalid distance types: expected a string distance type or a distance function');
    } else if (typeof options.distanceMetric === 'string' && typeof options.distanceFunction === 'function') {
        throw new Error('Invalid parameters: provide either a distance metric or function but not both');
    }

    if (typeof options.distanceMetric === 'string') {
        var normalizedDistanceMetric = options.distanceMetric.toLowerCase();
        if (normalizedDistanceMetric !== 'manhattan' && normalizedDistanceMetric !== 'euclidean'
            && normalizedDistanceMetric !== 'squaredeuclidean') {
            throw new Error('Invalid parameter value: Unknown distance metric \'' + options.distanceMetric + '\'');
        }
    }
}

function retrieveDistanceFunction(distanceMetric) {
    var normalizedDistanceMetric = distanceMetric.toLowerCase();
    var distanceFunction = null;
    if (normalizedDistanceMetric === 'manhattan') {
        distanceFunction = manhattan.distance;
    } else if (normalizedDistanceMetric === 'euclidean') {
        distanceFunction = euclidean.distance;
    } else if (normalizedDistanceMetric === 'squaredeuclidean') {
        distanceFunction = squaredEuclidean.distance;
    }

    return distanceFunction;
}

/**
 * Create a DTWOptions object
 * @class DTWOptions
 * @member {string} distanceMetric The distance metric to use: `'manhattan' | 'euclidean' | 'squaredEuclidean'`.
 * @member {function} distanceFunction The distance function to use. The function should accept two numeric arguments and return the numeric distance. e.g. function (a, b) { return a + b; }
 */

/**
 * Create a DTW object
 * @class DTW
 */
/**
 * Initializes a new instance of the `DTW`. If no options are provided the squared euclidean distance function is used.
 * @function DTW
 * @param {DTWOptions} [options] The options to initialize the dynamic time warping instance with.
 */
/**
 * Computes the optimal match between two provided sequences.
 * @method compute
 * @param {number[]} firstSequence The first sequence.
 * @param {number[]} secondSequence The second sequence.
 * @param {number} [window] The window parameter (for the locality constraint) to use.
 * @returns {number} The similarity between the provided temporal sequences.
 */
/**
 * Retrieves the optimal match between two provided sequences.
 * @method path
 * @returns {number[]} The array containing the optimal path points.
 */
var DTW = function (options) {
    var state = { distanceCostMatrix: null };
    if (typeof options === 'undefined') {
        state.distance = squaredEuclidean.distance;
    } else {
        validateOptions(options);
        if (typeof options.distanceMetric === 'string') {
            state.distance = retrieveDistanceFunction(options.distanceMetric);
        } else if (typeof options.distanceFunction === 'function') {
            state.distance = options.distanceFunction;
        }
    }

    this.compute = function (firstSequence, secondSequence, window) {
        var cost = Number.POSITIVE_INFINITY;
        if (typeof window === 'undefined') {
            cost = computeOptimalPath(firstSequence, secondSequence, state);
        } else if (typeof window === 'number') {
            cost = computeOptimalPathWithWindow(firstSequence, secondSequence, window, state);
        } else {
            throw new TypeError('Invalid window parameter type: expected a number');
        }

        return cost;
    };

    this.path = function () {
        var path = null;
        if (state.distanceCostMatrix instanceof Array) {
            path = retrieveOptimalPath(state);
        }

        return path;
    };
};

function validateComputeParameters(s, t) {
    validateSequence(s, 'firstSequence');
    validateSequence(t, 'secondSequence');
}

function computeOptimalPath(s, t, state) {
    validateComputeParameters(s, t);
    var start = new Date().getTime();
    state.m = s.length;
    state.n = t.length;
    var distanceCostMatrix = createMatrix(state.m, state.n, Number.POSITIVE_INFINITY);

    distanceCostMatrix[0][0] = state.distance(s[0], t[0]);

    for (var rowIndex = 1; rowIndex < state.m; rowIndex++) {
        var cost = state.distance(s[rowIndex], t[0]);
        distanceCostMatrix[rowIndex][0] = cost + distanceCostMatrix[rowIndex - 1][0];
    }

    for (var columnIndex = 1; columnIndex < state.n; columnIndex++) {
        var cost = state.distance(s[0], t[columnIndex]);
        distanceCostMatrix[0][columnIndex] = cost + distanceCostMatrix[0][columnIndex - 1];
    }

    for (var rowIndex = 1; rowIndex < state.m; rowIndex++) {
        for (var columnIndex = 1; columnIndex < state.n; columnIndex++) {
            var cost = state.distance(s[rowIndex], t[columnIndex]);
            distanceCostMatrix[rowIndex][columnIndex] =
                cost + Math.min(
                    distanceCostMatrix[rowIndex - 1][columnIndex],          // Insertion
                    distanceCostMatrix[rowIndex][columnIndex - 1],          // Deletion
                    distanceCostMatrix[rowIndex - 1][columnIndex - 1]);     // Match
        }
    }

    var end = new Date().getTime();
    var time = end - start;
    state.distanceCostMatrix = distanceCostMatrix;
    state.similarity = distanceCostMatrix[state.m - 1][state.n - 1];
    return state.similarity;
}

function computeOptimalPathWithWindow(s, t, w, state) {
    validateComputeParameters(s, t);
    var start = new Date().getTime();
    state.m = s.length;
    state.n = t.length;
    var window = Math.max(w, Math.abs(s.length - t.length));
    var distanceCostMatrix = createMatrix(state.m + 1, state.n + 1, Number.POSITIVE_INFINITY);
    distanceCostMatrix[0][0] = 0;

    for (var rowIndex = 1; rowIndex <= state.m; rowIndex++) {
        for (var columnIndex = Math.max(1, rowIndex - window); columnIndex <= Math.min(state.n, rowIndex + window); columnIndex++) {
            var cost = state.distance(s[rowIndex - 1], t[columnIndex - 1]);
            distanceCostMatrix[rowIndex][columnIndex] =
                cost + Math.min(
                distanceCostMatrix[rowIndex - 1][columnIndex],          // Insertion
                distanceCostMatrix[rowIndex][columnIndex - 1],          // Deletion
                distanceCostMatrix[rowIndex - 1][columnIndex - 1]);     // Match
        }
    }

    var end = new Date().getTime();
    var time = end - start;
    distanceCostMatrix.shift();
    distanceCostMatrix = distanceCostMatrix.map(function (row) {
        return row.slice(1, row.length);
    });
    state.distanceCostMatrix = distanceCostMatrix;
    state.similarity = distanceCostMatrix[state.m - 1][state.n - 1];
    return state.similarity;
}

function retrieveOptimalPath(state) {
    var start = new Date().getTime();

    var rowIndex = state.m - 1;
    var columnIndex = state.n - 1;
    var path = [[rowIndex, columnIndex]];
    var epsilon = 1e-14;
    while ((rowIndex > 0) || (columnIndex > 0)) {
        if ((rowIndex > 0) && (columnIndex > 0)) {
            var min = Math.min(
                state.distanceCostMatrix[rowIndex - 1][columnIndex],          // Insertion
                state.distanceCostMatrix[rowIndex][columnIndex - 1],          // Deletion
                state.distanceCostMatrix[rowIndex - 1][columnIndex - 1]);     // Match
            if (nearlyEqual(min, state.distanceCostMatrix[rowIndex - 1][columnIndex - 1], epsilon)) {
                rowIndex--;
                columnIndex--;
            } else if (nearlyEqual(min, state.distanceCostMatrix[rowIndex - 1][columnIndex], epsilon)) {
                rowIndex--;
            } else if (nearlyEqual(min, state.distanceCostMatrix[rowIndex][columnIndex - 1], epsilon)) {
                columnIndex--;
            }
        } else if ((rowIndex > 0) && (columnIndex === 0)) {
            rowIndex--;
        } else if ((rowIndex === 0) && (columnIndex > 0)) {
            columnIndex--;
        }

        path.push([rowIndex, columnIndex]);
    }

    var end = new Date().getTime();
    var time = end - start;
    return path.reverse();
}


// 相似度计算
function fastDtw(a, b) {
    // 欧氏距离，可选曼哈顿距离 manhattan，或者欧式距离平方 squaredEuclidean
    const options = { distanceMetric: 'euclidean' };
    const dtwIns = new DTW(options);
    const distance = dtwIns.compute(a, b);
    return distance
}


function calculateAngle(a, b, c) {
    // 获取点 a, b, c 的坐标
    const x1 = a.x;
    const y1 = a.y;
    const x2 = b.x;
    const y2 = b.y;
    const x3 = c.x;
    const y3 = c.y;
    
    // 计算角度
    let angle = Math.atan2(y3 - y2, x3 - x2) - Math.atan2(y1 - y2, x1 - x2);
    
    // 将弧度转换为角度
    angle = angle * (180 / Math.PI);
    
    // 确保角度为正值
    if (angle < 0) {
        angle += 360;
    }
  
    return angle;
}


const POSE_LANDMARKS = {
    "LEFT_SHOULDER": 6,
    "RIGHT_SHOULDER": 5,
    "LEFT_ELBOW": 8,
    "RIGHT_ELBOW": 7,
    "LEFT_WRIST": 10,
    "RIGHT_WRIST": 9,
    "LEFT_HIP": 12,
    "RIGHT_HIP": 11,
    "LEFT_KNEE": 14,
    "RIGHT_KNEE": 13,
    "LEFT_ANKLE": 16,
    "RIGHT_ANKLE": 15,
}

// 计算角度
function getAnglesData (results) {
    const LEFT_SHOULDER = results[POSE_LANDMARKS["LEFT_SHOULDER"]];
    const RIGHT_SHOULDER = results[POSE_LANDMARKS["RIGHT_SHOULDER"]];
    const LEFT_ELBOW = results[POSE_LANDMARKS["LEFT_ELBOW"]];
    const RIGHT_ELBOW = results[POSE_LANDMARKS["RIGHT_ELBOW"]];
    const LEFT_WRIST = results[POSE_LANDMARKS["LEFT_WRIST"]];
    const RIGHT_WRIST = results[POSE_LANDMARKS["RIGHT_WRIST"]];
    const LEFT_HIP = results[POSE_LANDMARKS["LEFT_HIP"]];
    const RIGHT_HIP = results[POSE_LANDMARKS["RIGHT_HIP"]];
    const LEFT_KNEE = results[POSE_LANDMARKS["LEFT_KNEE"]];
    const RIGHT_KNEE = results[POSE_LANDMARKS["RIGHT_KNEE"]];
    const LEFT_ANKLE = results[POSE_LANDMARKS["LEFT_ANKLE"]];
    const RIGHT_ANKLE = results[POSE_LANDMARKS["RIGHT_ANKLE"]];

    return [
        calculateAngle(RIGHT_SHOULDER, LEFT_SHOULDER, LEFT_ELBOW),
        calculateAngle(LEFT_SHOULDER, LEFT_ELBOW, LEFT_WRIST),
        calculateAngle(LEFT_SHOULDER, RIGHT_SHOULDER, RIGHT_ELBOW),
        calculateAngle(RIGHT_SHOULDER, RIGHT_ELBOW, RIGHT_WRIST),
        calculateAngle(RIGHT_HIP, LEFT_HIP, LEFT_KNEE),
        calculateAngle(LEFT_HIP, LEFT_KNEE, LEFT_ANKLE),
        calculateAngle(LEFT_HIP, RIGHT_HIP, RIGHT_KNEE),
        calculateAngle(RIGHT_HIP, RIGHT_KNEE, RIGHT_ANKLE)
    ]
}

function processAngle(angle) {
    if (angle > 180) {
        return 360 - angle
    } else {
        return angle
    }
    // return angle
}


function calculateScore(angleArrVideo, angleArrReal) {
    // 不等长的时候，取最长的
    const maxLength = angleArrVideo.length >= angleArrReal.length ? angleArrVideo.length : angleArrReal.length
    let finalScore = 0;
    for (let i = 0; i < 8; i++) {
        const angleSingleVideo = angleArrVideo.map(item => processAngle(item[i]))
        const angleSingleReal =angleArrReal.map(item => processAngle(item[i]))
        const distance = fastDtw(angleSingleVideo, angleSingleReal)
        // console.log("distance----------", distance)
        if (i < 4) {
            finalScore += distance * 3
        } else {
            finalScore += distance
        }
        // console.log(i, finalScore)
    }
    // 16 是因为上面的finalScore的权重值加起来得到的
    finalScore = finalScore / 16
    // 归一化评分
    // 如果 80 * len(pose_arr_1) 代表理想匹配的最大值，那么这个值比100稍小，可能是为了减少对理想匹配的上限，考虑到实际数据中不可能达到完全一致的情况
    // 通过乘以0.8对分母进行缩放，可以增加最终得分的敏感度，使得评分差异更加明显。
    finalScore = (90 * maxLength - finalScore)/(0.9 * maxLength)
    finalScore = (finalScore * finalScore ) / 100
    // console.log("finalScore", finalScore)
    // 偶尔有发现，分数超过100的情况，不知道具体怎么算出来，做一个值处理
    if (finalScore > 100) finalScore = finalScore % 100
    return Math.round(finalScore)
}

calculateScore(userData, videoData)
